# คู่มือการใช้งานระบบจัดการกิจกรรม (Activities) - SoloShop

## ✅ ฟีเจอร์ใหม่ที่เพิ่มเข้ามา

### 🎯 ฟีเจอร์หลัก
1. **การจัดการรูปภาพแบบสมบูรณ์**
   - เพิ่มรูปภาพหลายรูปพร้อมคำบรรยาย
   - แก้ไขคำบรรยายรูปภาพที่มีอยู่แล้ว
   - เปลี่ยนรูปภาพโดยไม่ต้องลบแล้วเพิ่มใหม่
   - ลบรูปภาพแต่ละรูปได้

2. **การจัดเรียงรูปภาพ**
   - ลากเพื่อจัดเรียงลำดับรูปภาพ
   - อัปเดตลำดับแบบ Real-time
   - แสดงหมายเลขลำดับชัดเจน

3. **UI/UX ที่ปรับปรุงแล้ว**
   - ตัวอย่างรูปภาพแบบ Real-time
   - การแจ้งเตือนและข้อผิดพลาดที่ชัดเจน
   - ปุ่มจัดการที่เข้าใจง่าย
   - การออกแบบที่ตอบสนอง (Responsive)

### 🛠️ ฟีเจอร์ที่ปรับปรุง

#### 1. หน้าเพิ่มกิจกรรม (Create)
- **UI ใหม่**: กรอบรูปภาพที่สวยงาม พร้อมตัวอย่าง
- **การเพิ่มรูปภาพ**: สามารถเพิ่มรูปภาพหลายรูปได้ง่าย
- **ตัวอย่างรูปภาพ**: แสดงตัวอย่างทันทีเมื่อเลือกไฟล์
- **การลบรูปภาพ**: ลบรูปภาพก่อนบันทึกได้

#### 2. หน้าแก้ไขกิจกรรม (Edit)
- **การจัดการรูปภาพขั้นสูง**: 
  - ✏️ แก้ไขคำบรรยาย
  - 🔄 เปลี่ยนรูปภาพ
  - 🗑️ ลบรูปภาพ
  - ↕️ จัดเรียงลำดับ
- **Modal สำหรับแก้ไข**: ป๊อปอัพสำหรับแก้ไขคำบรรยายและเปลี่ยนรูปภาพ
- **Drag & Drop**: ลากเพื่อจัดเรียงลำดับรูปภาพ

#### 3. หน้ารายการกิจกรรม (Index)
- **แสดงจำนวนรูปภาพ**: ไอคอนและจำนวนรูปภาพที่ชัดเจน
- **สีสันที่เหมาะสม**: ใช้สีแยกแยะสถานะต่างๆ

## 🎮 วิธีการใช้งาน

### การเพิ่มกิจกรรมใหม่
1. ไปที่ **จัดการกิจกรรม** > **เพิ่มกิจกรรมใหม่**
2. กรอกข้อมูลพื้นฐาน (ชื่อ, รายละเอียด, หมวดหมู่)
3. เลือกรูปภาพหน้าปก (ไม่บังคับ)
4. เพิ่มรูปภาพในแกลเลอรี่:
   - คลิก "เลือกรูปภาพ" 
   - ใส่คำบรรยาย (ไม่บังคับ)
   - ดูตัวอย่างรูปภาพ
   - คลิก "เพิ่มรูปภาพอีก" เพื่อเพิ่มรูปเพิ่มเติม
5. คลิก **บันทึกกิจกรรม**

### การแก้ไขกิจกรรม
1. ไปที่ **จัดการกิจกรรม** > คลิกปุ่ม **แก้ไข** ที่กิจกรรมที่ต้องการ
2. แก้ไขข้อมูลพื้นฐานได้ตามต้องการ
3. จัดการรูปภาพที่มีอยู่:
   - **✏️ แก้ไขคำบรรยาย**: คลิกปุ่มดินสอสีน้ำเงิน
   - **🔄 เปลี่ยนรูปภาพ**: คลิกปุ่มลูกศรสีเหลือง
   - **🗑️ ลบรูปภาพ**: คลิกปุ่มถังขยะสีแดง
   - **↕️ จัดเรียงลำดับ**: ลากที่ไอคอน grip (≡≡)
4. เพิ่มรูปภาพใหม่ในส่วน "เพิ่มรูปภาพใหม่ในแกลเลอรี่"
5. คลิก **บันทึกการแก้ไข**

### การจัดเรียงรูปภาพ
1. ในหน้าแก้ไขกิจกรรม
2. ลากที่ไอคอน **≡≡** (grip) ที่มุมซ้ายบนของรูปภาพ
3. ลากไปยังตำแหน่งที่ต้องการ
4. ระบบจะบันทึกลำดับใหม่อัตโนมัติ

## 🔧 ข้อมูลทางเทคนิค

### ไฟล์ที่เกี่ยวข้อง
- `app/Http/Controllers/Admin/ActivityController.php` - Controller หลัก
- `resources/views/admin/activities/` - Views ทั้งหมด
- `routes/web.php` - Routes สำหรับ API
- `app/Models/Activity.php` - Model กิจกรรม
- `app/Models/ActivityImage.php` - Model รูปภาพกิจกรรม

### API Endpoints ใหม่
- `PUT /admin/activities/images/{image}/caption` - อัปเดตคำบรรยาย
- `PUT /admin/activities/{activity}/images/order` - อัปเดตลำดับรูปภาพ
- `POST /admin/activities/images/{image}/replace` - เปลี่ยนรูปภาพ
- `DELETE /admin/activities/{activity}/images/{image}` - ลบรูปภาพ

### JavaScript Libraries
- **SortableJS**: สำหรับ Drag & Drop การจัดเรียง
- **Bootstrap Modal**: สำหรับ Pop-up แก้ไข
- **FileReader API**: สำหรับตัวอย่างรูปภาพ

## 🎨 การปรับแต่ง CSS

### สไตล์หลัก
```css
.gallery-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.gallery-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}
```

### การตอบสนอง (Responsive)
- รูปภาพปรับขนาดอัตโนมัติ
- Layout เปลี่ยนตาม screen size
- ปุ่มและ UI elements ใช้งานได้บนมือถือ

## 🔒 ความปลอดภัย

### การตรวจสอบไฟล์
- ตรวจสอบประเภทไฟล์ (JPEG, JPG, PNG, GIF, WebP)
- จำกัดขนาดไฟล์ (สูงสุด 2MB)
- ใช้ ImageHelper สำหรับการประมวลผล

### การตรวจสอบสิทธิ์
- ต้องล็อกอินเป็น Admin
- ตรวจสอบ CSRF Token
- ตรวจสอบความเป็นเจ้าของข้อมูล

## 🚀 การปรับปรุงในอนาคต

### ฟีเจอร์ที่อาจเพิ่ม
- [ ] การอัปโหลดรูปภาพแบบ Bulk
- [ ] การ Crop รูปภาพ
- [ ] การเพิ่ม Watermark
- [ ] การสำรองข้อมูลรูปภาพ
- [ ] การจัดการ SEO สำหรับรูปภาพ

### การเพิ่มประสิทธิภาพ
- [ ] Lazy Loading สำหรับรูปภาพ
- [ ] Image Compression ขั้นสูง
- [ ] CDN Integration
- [ ] Progressive Web App Support

---

## 📞 การสนับสนุน

หากพบปัญหาหรือต้องการความช่วยเหลือ:
1. ตรวจสอบ Console ของเบราว์เซอร์
2. ดู Log ไฟล์ของ Laravel
3. ตรวจสอบการตั้งค่า GD Extension
4. ตรวจสอบสิทธิ์การเขียนไฟล์ในโฟลเดอร์ storage

**สร้างโดย**: Augment Agent  
**วันที่อัปเดต**: {{ date('d/m/Y') }}  
**เวอร์ชัน**: 2.0
