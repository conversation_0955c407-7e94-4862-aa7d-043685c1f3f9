# คู่มือการใช้งานระบบหลังบ้าน SoloShop

## ข้อมูลการเข้าสู่ระบบ

### Admin Account
- **Email:** <EMAIL>
- **Password:** admin123

### Demo User Account
- **Email:** <EMAIL>
- **Password:** user123

## ฟีเจอร์หลักของระบบหลังบ้าน

### 1. แดชบอร์ด (Dashboard)
- ภาพรวมสถิติของระบบ
- จำนวนบริการ, แพ็กเกจ, บทความ
- ข้อความติดต่อใหม่
- การดำเนินการด่วน
- รายการล่าสุด

### 2. จัดการบริการ (Services Management)
- **เพิ่มบริการใหม่:** สร้างบริการพร้อมรูปภาพ, ราคา, รายละเอียด
- **แก้ไขบริการ:** อัปเดตข้อมูลบริการที่มีอยู่
- **ลบบริการ:** ลบบริการที่ไม่ต้องการ (รวมรูปภาพ)
- **ค้นหาบริการ:** ค้นหาตามชื่อหรือรายละเอียด
- **อัปโหลดรูปภาพ:** รองรับ JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB)

### 3. จัดการแพ็กเกจ (Packages Management)
- **เพิ่มแพ็กเกจใหม่:** สร้างแพ็กเกจพร้อมรูปภาพ, ราคา, รายละเอียด
- **แก้ไขแพ็กเกจ:** อัปเดตข้อมูลแพ็กเกจที่มีอยู่
- **ลบแพ็กเกจ:** ลบแพ็กเกจที่ไม่ต้องการ (รวมรูปภาพ)
- **ค้นหาแพ็กเกจ:** ค้นหาตามชื่อหรือรายละเอียด
- **อัปโหลดรูปภาพ:** รองรับ JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB)

### 4. จัดการบทความ (Articles Management)
- **เพิ่มบทความใหม่:** สร้างบทความพร้อมรูปภาพ, วันที่เผยแพร่
- **แก้ไขบทความ:** อัปเดตข้อมูลบทความที่มีอยู่
- **ลบบทความ:** ลบบทความที่ไม่ต้องการ (รวมรูปภาพ)
- **ค้นหาบทความ:** ค้นหาตามหัวข้อหรือเนื้อหา
- **กำหนดวันที่เผยแพร่:** สามารถกำหนดวันที่เผยแพร่ได้
- **อัปโหลดรูปภาพ:** รองรับ JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB)

### 5. จัดการข้อความติดต่อ (Contact Management)
- **ดูข้อความทั้งหมด:** แสดงรายการข้อความติดต่อ
- **กรองข้อความ:** แยกตามสถานะ (ยังไม่อ่าน/อ่านแล้ว)
- **ดูรายละเอียด:** ดูข้อมูลผู้ติดต่อและข้อความแบบละเอียด
- **ทำเครื่องหมายอ่านแล้ว:** เปลี่ยนสถานะข้อความ
- **ลบข้อความ:** ลบข้อความที่ไม่ต้องการ
- **ติดต่อกลับ:** ลิงก์โทรศัพท์และอีเมลโดยตรง

### 6. จัดการหน้าแรก (Homepage Management)
- **แก้ไขเนื้อหาหน้าแรก:** อัปเดตข้อมูลส่วนต่างๆ ของหน้าแรก
- **อัปโหลดรูปภาพ:** เปลี่ยนรูปภาพประกอบ
- **แก้ไขปุ่ม:** เปลี่ยนข้อความและลิงก์ปุ่ม

## ฟีเจอร์พิเศษ

### การจัดการรูปภาพ
- **อัปโหลดอัตโนมัติ:** ระบบจะปรับขนาดรูปภาพอัตโนมัติ
- **ตรวจสอบคุณภาพ:** ตรวจสอบประเภทและขนาดไฟล์
- **ลบอัตโนมัติ:** เมื่อลบข้อมูล รูปภาพจะถูกลบด้วย
- **แสดงตัวอย่าง:** ดูรูปภาพปัจจุบันก่อนเปลี่ยน

### การค้นหา
- **ค้นหาแบบเรียลไทม์:** ผลลัพธ์แสดงทันทีขณะพิมพ์
- **ค้นหาหลายฟิลด์:** ค้นหาได้ทั้งชื่อและรายละเอียด
- **ไม่ต้องรีเฟรชหน้า:** ใช้ JavaScript ในการกรอง

### การแจ้งเตือน
- **ข้อความสำเร็จ:** แจ้งเตือนเมื่อดำเนินการสำเร็จ
- **ข้อความข้อผิดพลาด:** แสดงข้อผิดพลาดที่เกิดขึ้น
- **ยืนยันการลบ:** ป้องกันการลบโดยไม่ตั้งใจ

### การนำทาง
- **Breadcrumb:** แสดงตำแหน่งปัจจุบัน
- **เมนูด้านข้าง:** เข้าถึงฟีเจอร์ต่างๆ ได้ง่าย
- **ปุ่มย้อนกลับ:** กลับไปหน้าก่อนหน้าได้ทันที

## การใช้งานเบื้องต้น

### 1. เข้าสู่ระบบ
1. ไปที่ `/login`
2. ใส่อีเมลและรหัสผ่าน
3. ระบบจะพาไปหน้าแดชบอร์ดอัตโนมัติ

### 2. เพิ่มบริการใหม่
1. คลิก "จัดการบริการ" ในเมนูด้านข้าง
2. คลิก "เพิ่มบริการใหม่"
3. กรอกข้อมูล: ชื่อ, รายละเอียด, ราคา
4. อัปโหลดรูปภาพ (ถ้ามี)
5. คลิก "บันทึกบริการ"

### 3. จัดการข้อความติดต่อ
1. คลิก "จัดการข้อความติดต่อ" ในเมนูด้านข้าง
2. ดูรายการข้อความ (ข้อความใหม่จะมีสีเหลือง)
3. คลิกไอคอน "ตา" เพื่อดูรายละเอียด
4. ระบบจะทำเครื่องหมายว่าอ่านแล้วอัตโนมัติ

### 4. ค้นหาข้อมูล
1. ใช้ช่องค้นหาที่มุมขวาบนของตาราง
2. พิมพ์คำที่ต้องการค้นหา
3. ผลลัพธ์จะแสดงทันที

## เคล็ดลับการใช้งาน

### การอัปโหลดรูปภาพ
- ใช้รูปภาพที่มีคุณภาพดี ขนาดไม่เกิน 2MB
- รูปภาพจะถูกปรับขนาดเป็น 800x600 พิกเซลอัตโนมัติ
- รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP

### การเขียนเนื้อหา
- ใช้ภาษาที่เข้าใจง่าย ชัดเจน
- เขียนรายละเอียดให้ครบถ้วน
- ใส่ราคาที่เหมาะสม

### การจัดการข้อมูล
- ตรวจสอบข้อมูลก่อนบันทึก
- ใช้ฟีเจอร์ค้นหาเพื่อหาข้อมูลได้เร็ว
- ลบข้อมูลที่ไม่ใช้แล้วเป็นประจำ

## การแก้ไขปัญหา

### ไม่สามารถอัปโหลดรูปภาพได้
- ตรวจสอบขนาดไฟล์ (ต้องไม่เกิน 2MB)
- ตรวจสอบประเภทไฟล์ (ต้องเป็น JPEG, JPG, PNG, GIF, WebP)
- ลองรีเฟรชหน้าและลองใหม่

### ข้อมูลไม่แสดง
- ตรวจสอบการเชื่อมต่ออินเทอร์เน็ต
- รีเฟรชหน้าเว็บ
- ล็อกเอาท์และล็อกอินใหม่

### ลืมรหัสผ่าน
- ติดต่อผู้ดูแลระบบเพื่อรีเซ็ตรหัสผ่าน

## การสนับสนุน

หากมีปัญหาหรือข้อสงสัยในการใช้งาน กรุณาติดต่อทีมพัฒนาระบบ

---

**หมายเหตุ:** คู่มือนี้อาจมีการอัปเดตเป็นระยะๆ กรุณาตรวจสอบเวอร์ชันล่าสุดเสมอ
