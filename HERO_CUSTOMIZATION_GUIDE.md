# คู่มือการปรับแต่งส่วน Hero Section ของเว็บไซต์

## ภาพรวม

ระบบ admin backend ของ SoloShop ตอนนี้รองรับการปรับแต่งส่วน Hero Section (ส่วนหัวหลักของหน้าแรก) ได้แล้ว ซึ่งรวมถึง:

1. **ไอคอนหลัก** - รูปภาพที่แสดงในส่วน Hero Section
2. **ข้อความหลัก** - ข้อความที่แสดงเป็นหัวข้อหลักของหน้าแรก

## วิธีการเข้าถึง

1. เข้าสู่ระบบ Admin ที่ `/admin`
2. ไปที่เมนู **"การตั้งค่าเว็บไซต์"** หรือ `/admin/settings`
3. มองหาส่วน **"ข้อมูลพื้นฐาน"** และ **"รูปภาพ"**

## ฟีเจอร์ที่เพิ่มใหม่

### 1. ข้อความหลักหน้าแรก (Hero Title)

**ตำแหน่ง:** ส่วนข้อมูลพื้นฐาน

**คำอธิบาย:** ข้อความที่จะแสดงเป็นหัวข้อหลักในส่วน Hero Section ของหน้าแรก

**วิธีใช้:**
- กรอกข้อความที่ต้องการในช่อง "ข้อความหลักหน้าแรก"
- ตัวอย่าง: "ยินดีต้อนรับสู่ SoloShop"
- หากไม่กรอก ระบบจะใช้ค่าเริ่มต้น: "ยินดีต้อนรับสู่ [ชื่อเว็บไซต์]"

### 2. ไอคอนหลักหน้าแรก (Hero Icon)

**ตำแหน่ง:** ส่วนรูปภาพ

**คำอธิบาย:** รูปภาพที่จะแสดงเป็นไอคอนหลักในส่วน Hero Section

**ข้อกำหนดไฟล์:**
- รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP
- ขนาดไฟล์: ไม่เกิน 2MB
- ขนาดแนะนำ: 300x300 พิกเซล หรือสัดส่วนสี่เหลี่ยมจัตุรัส

**วิธีใช้:**
1. คลิก "เลือกไฟล์" ในส่วน "ไอคอนหลักหน้าแรก"
2. เลือกรูปภาพที่ต้องการ
3. คลิก "บันทึกการตั้งค่า"

**การลบไอคอน:**
- หากมีไอคอนอยู่แล้ว จะมีปุ่ม "ลบไอคอน" สีแดง
- คลิกเพื่อลบไอคอนปัจจุบัน
- หากไม่มีไอคอน ระบบจะใช้ไอคอนตะกร้าสินค้าเป็นค่าเริ่มต้น

## การแสดงผลในหน้าเว็บไซต์

### ส่วนหัวข้อหลัก (ด้านซ้าย)
- แสดงไอคอนหลัก (หากมี) หรือไอคอนตะกร้าสินค้า (หากไม่มี)
- แสดงข้อความหลัก (หากมี) หรือ "ยินดีต้อนรับสู่ [ชื่อเว็บไซต์]" (หากไม่มี)

### ส่วนรูปภาพใหญ่ (ด้านขวา)
- แสดงไอคอนหลักขนาดใหญ่ (หากมี) หรือไอคอนตะกร้าสินค้าขนาดใหญ่ (หากไม่มี)

## ตัวอย่างการใช้งาน

### กรณีที่ 1: ใช้ไอคอนและข้อความเริ่มต้น
- ไม่อัปโหลดไอคอน
- ไม่กรอกข้อความหลัก
- ผลลัพธ์: แสดงไอคอนตะกร้าสินค้าและข้อความ "ยินดีต้อนรับสู่ SoloShop"

### กรณีที่ 2: ปรับแต่งข้อความเท่านั้น
- ไม่อัปโหลดไอคอน
- กรอกข้อความหลัก: "ยินดีต้อนรับสู่ร้านค้าออนไลน์ของเรา"
- ผลลัพธ์: แสดงไอคอนตะกร้าสินค้าและข้อความที่กำหนด

### กรณีที่ 3: ปรับแต่งทั้งไอคอนและข้อความ
- อัปโหลดไอคอนโลโก้ของร้าน
- กรอกข้อความหลัก: "ยินดีต้อนรับสู่ MyShop"
- ผลลัพธ์: แสดงไอคอนและข้อความที่กำหนด

## การแก้ไขปัญหา

### ปัญหา: ไอคอนไม่แสดง
**สาเหตุ:**
- ไฟล์รูปภาพเสียหาย
- ขนาดไฟล์เกิน 2MB
- รูปแบบไฟล์ไม่รองรับ

**วิธีแก้:**
- ตรวจสอบขนาดและรูปแบบไฟล์
- ลองอัปโหลดไฟล์ใหม่
- ใช้รูปภาพที่มีคุณภาพดีและขนาดเหมาะสม

### ปัญหา: ข้อความไม่แสดง
**สาเหตุ:**
- ไม่ได้บันทึกการตั้งค่า
- มีข้อผิดพลาดในการส่งฟอร์ม

**วิธีแก้:**
- ตรวจสอบว่าคลิก "บันทึกการตั้งค่า" แล้ว
- รีเฟรชหน้าเว็บไซต์
- ตรวจสอบข้อความแสดงข้อผิดพลาด

## ข้อมูลเทคนิค

### ไฟล์ที่เกี่ยวข้อง
- **Model:** `App\Models\SiteSetting`
- **Controller:** `App\Http\Controllers\SiteSettingController`
- **View:** `resources/views/admin/settings/index.blade.php`
- **Home View:** `resources/views/home.blade.php`
- **Migration:** `2025_07_13_092615_add_hero_fields_to_site_settings_table`

### ฟิลด์ในฐานข้อมูล
- `hero_icon` (varchar) - เส้นทางไฟล์ไอคอนหลัก
- `hero_title` (text) - ข้อความหลักหน้าแรก

### Routes ที่เกี่ยวข้อง
- `GET /admin/settings` - หน้าการตั้งค่า
- `PUT /admin/settings` - อัปเดตการตั้งค่า
- `DELETE /admin/settings/remove-hero-icon` - ลบไอคอนหลัก

## การสำรองข้อมูล

แนะนำให้สำรองข้อมูลก่อนทำการเปลี่ยนแปลง:
1. สำรองฐานข้อมูล
2. สำรองโฟลเดอร์ `storage/app/public/`
3. บันทึกการตั้งค่าปัจจุบันไว้

## การอัปเดตในอนาคต

ฟีเจอร์ที่อาจเพิ่มในอนาคต:
- การปรับแต่งสีพื้นหลัง Hero Section
- การเพิ่มปุ่มเพิ่มเติมใน Hero Section
- การปรับแต่งข้อความรอง (subtitle)
- การเลือกเลย์เอาต์ Hero Section
