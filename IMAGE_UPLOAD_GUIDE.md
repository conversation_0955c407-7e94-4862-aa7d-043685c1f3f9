# คู่มือการใช้งานระบบอัปโหลดรูปภาพ - SoloShop

## ✅ สิ่งที่ได้แก้ไขและปรับปรุงแล้ว

### 1. ติดตั้ง Intervention Image Package
- ติดตั้ง `intervention/image` เวอร์ชัน 3.11
- รองรับการประมวลผลรูปภาพด้วย GD Driver
- ระบบ fallback หาก GD extension ไม่พร้อมใช้งาน

### 2. ปรับปรุง ImageHelper Class
- **ตำแหน่ง**: `app/Helpers/ImageHelper.php`
- **ฟีเจอร์ใหม่**:
  - การ resize รูปภาพอัตโนมัติ (ขนาดสูงสุด 800x600px)
  - การตรวจสอบ GD extension
  - ระบบ error handling และ logging
  - การสร้างโฟลเดอร์อัตโนมัติ
  - การลบไฟล์ชั่วคราว

### 3. ปรับปรุง Views ทั้งหมด
- **Admin Views**: ใช้ `ImageHelper::getImageUrl()` แทน `asset('storage/')`
- **Public Views**: แสดงรูป placeholder เมื่อไม่มีรูปภาพ
- **รูป Placeholder**: สร้าง SVG placeholder ที่ `public/images/no-image.svg`

### 4. เพิ่ม Middleware ตรวจสอบการอัปโหลด
- **ตำแหน่ง**: `app/Http/Middleware/CheckFileUpload.php`
- **การตรวจสอบ**:
  - ขนาดไฟล์ไม่เกิน 2MB
  - ประเภทไฟล์: JPEG, JPG, PNG, GIF, WebP
  - ความถูกต้องของการอัปโหลด

### 5. ปรับปรุง Routes
- เพิ่ม middleware `check.file.upload` สำหรับ routes ที่มีการอัปโหลดไฟล์
- แยก routes ที่มีและไม่มีการอัปโหลดไฟล์

## 📁 โครงสร้างโฟลเดอร์รูปภาพ

```
storage/app/public/
├── services/       # รูปภาพบริการ
├── packages/       # รูปภาพแพ็กเกจ
├── articles/       # รูปภาพบทความ
├── homepage/       # รูปภาพหน้าแรก
└── temp/          # ไฟล์ชั่วคราว (จะถูกลบอัตโนมัติ)

public/
├── storage/       # Symbolic link ไปยัง storage/app/public
└── images/
    └── no-image.svg  # รูป placeholder
```

## 🔧 การใช้งาน ImageHelper

### การอัปโหลดและ resize รูปภาพ
```php
use App\Helpers\ImageHelper;

// อัปโหลดและ resize อัตโนมัติ
$imagePath = ImageHelper::uploadAndResize($request->file('image'), 'services');

// กำหนดขนาดสูงสุดเอง
$imagePath = ImageHelper::uploadAndResize($request->file('image'), 'services', 1200, 800);
```

### การแสดงรูปภาพ
```php
// ใน Controller
$imageUrl = ImageHelper::getImageUrl($model->image);

// ใน Blade Template
<img src="{{ \App\Helpers\ImageHelper::getImageUrl($model->image) }}" alt="รูปภาพ">
```

### การลบรูปภาพ
```php
// ลบรูปภาพเมื่อลบข้อมูล
ImageHelper::deleteImage($model->image);
```

## ⚙️ การตั้งค่าที่สำคัญ

### 1. ตรวจสอบ GD Extension
```bash
php -m | grep -i gd
```

### 2. ตรวจสอบการตั้งค่า PHP
```ini
; ใน php.ini
upload_max_filesize = 2M
post_max_size = 8M
max_execution_time = 30
memory_limit = 128M
```

### 3. สร้าง Storage Link
```bash
php artisan storage:link
```

## 🐛 การแก้ไขปัญหา

### ปัญหา: รูปภาพไม่แสดง
1. ตรวจสอบ storage link: `ls -la public/storage`
2. ตรวจสอบสิทธิ์โฟลเดอร์: `chmod 755 storage/app/public`
3. ตรวจสอบ log: `tail -f storage/logs/laravel.log`

### ปัญหา: การอัปโหลดล้มเหลว
1. ตรวจสอบขนาดไฟล์และประเภทไฟล์
2. ตรวจสอบ GD extension
3. ตรวจสอบพื้นที่ว่างในเซิร์ฟเวอร์

### ปัญหา: รูปภาพไม่ถูก resize
1. ตรวจสอบ GD extension
2. ตรวจสอบ memory limit
3. ดู error log สำหรับรายละเอียด

## 📝 ข้อมูลเพิ่มเติม

### ไฟล์ที่ได้รับการปรับปรุง:
- `app/Helpers/ImageHelper.php` - ระบบจัดการรูปภาพ
- `app/Http/Middleware/CheckFileUpload.php` - ตรวจสอบการอัปโหลด
- `routes/web.php` - เพิ่ม middleware
- Views ทั้งหมดที่เกี่ยวข้องกับการแสดงรูปภาพ

### การทดสอบ:
1. เข้าสู่ระบบ admin: `http://localhost:8000/admin`
2. ทดสอบเพิ่มบริการใหม่พร้อมรูปภาพ
3. ทดสอบแก้ไขรูปภาพ
4. ตรวจสอบการแสดงผลในหน้าบ้าน

### ข้อมูล Admin:
- Email: <EMAIL>
- Password: admin123
