# คู่มือการติดตั้งระบบ SoloShop

## ความต้องการของระบบ

### Server Requirements
- PHP >= 8.0
- MySQL >= 5.7 หรือ MariaDB >= 10.3
- Apache หรือ Nginx
- Composer
- Node.js และ NPM (สำหรับ frontend assets)

### PHP Extensions
- BCMath PHP Extension
- Ctype PHP Extension
- Fileinfo PHP Extension
- JSON PHP Extension
- Mbstring PHP Extension
- OpenSSL PHP Extension
- PDO PHP Extension
- Tokenizer PHP Extension
- XML PHP Extension
- GD PHP Extension (สำหรับการจัดการรูปภาพ)

## การติดตั้ง

### 1. Clone Repository
```bash
git clone https://github.com/your-username/soloshop.git
cd soloshop
```

### 2. ติดตั้ง Dependencies
```bash
# ติดตั้ง PHP dependencies
composer install

# ติดตั้ง Node.js dependencies
npm install
```

### 3. ตั้งค่า Environment
```bash
# คัดลอกไฟล์ environment
cp .env.example .env

# สร้าง application key
php artisan key:generate
```

### 4. ตั้งค่าฐานข้อมูล
แก้ไขไฟล์ `.env`:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=soloshop
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 5. สร้างฐานข้อมูลและตาราง
```bash
# สร้างฐานข้อมูล (ถ้ายังไม่มี)
mysql -u root -p -e "CREATE DATABASE soloshop;"

# รัน migrations
php artisan migrate

# รัน seeders (ข้อมูลตัวอย่าง)
php artisan db:seed
```

### 6. ตั้งค่า Storage
```bash
# สร้าง symbolic link สำหรับ storage
php artisan storage:link

# ตั้งค่าสิทธิ์ folder
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

### 7. Compile Assets
```bash
# สำหรับ development
npm run dev

# สำหรับ production
npm run build
```

### 8. ตั้งค่า Web Server

#### Apache (.htaccess)
ไฟล์ `.htaccess` ถูกสร้างไว้แล้วใน `public` folder

#### Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/soloshop/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## การตั้งค่าเพิ่มเติม

### 1. Mail Configuration
แก้ไขไฟล์ `.env` สำหรับการส่งอีเมล:
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
```

### 2. File Upload Configuration
แก้ไขไฟล์ `php.ini`:
```ini
upload_max_filesize = 2M
post_max_size = 2M
max_execution_time = 300
memory_limit = 256M
```

### 3. Image Processing (Intervention Image)
ติดตั้ง Intervention Image สำหรับการจัดการรูปภาพ:
```bash
composer require intervention/image
```

## การสร้างข้อมูลเริ่มต้น

### 1. สร้าง Admin User
```bash
php artisan db:seed --class=AdminUserSeeder
```

### 2. สร้างข้อมูลตัวอย่าง
```bash
php artisan db:seed
```

## การทดสอบระบบ

### 1. เริ่มต้น Development Server
```bash
php artisan serve
```

### 2. เข้าถึงระบบ
- **Frontend:** http://localhost:8000
- **Admin Login:** http://localhost:8000/login
  - Email: <EMAIL>
  - Password: admin123

### 3. ทดสอบฟีเจอร์
- ทดสอบการล็อกอิน/ล็อกเอาท์
- ทดสอบการเพิ่ม/แก้ไข/ลบข้อมูล
- ทดสอบการอัปโหลดรูปภาพ
- ทดสอบการค้นหา

## การ Deploy สู่ Production

### 1. ตั้งค่า Environment
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com
```

### 2. Optimize Application
```bash
# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Optimize autoloader
composer install --optimize-autoloader --no-dev
```

### 3. ตั้งค่า SSL Certificate
```bash
# ใช้ Let's Encrypt (Certbot)
sudo certbot --nginx -d your-domain.com
```

### 4. ตั้งค่า Cron Jobs
เพิ่มใน crontab:
```bash
* * * * * cd /path/to/soloshop && php artisan schedule:run >> /dev/null 2>&1
```

## การแก้ไขปัญหา

### 1. Permission Issues
```bash
sudo chown -R www-data:www-data /path/to/soloshop
sudo chmod -R 755 /path/to/soloshop
sudo chmod -R 775 /path/to/soloshop/storage
sudo chmod -R 775 /path/to/soloshop/bootstrap/cache
```

### 2. Database Connection Error
- ตรวจสอบข้อมูลในไฟล์ `.env`
- ตรวจสอบว่าฐานข้อมูลถูกสร้างแล้ว
- ตรวจสอบสิทธิ์ของ user ฐานข้อมูล

### 3. 500 Internal Server Error
- ตรวจสอบ log ใน `storage/logs/laravel.log`
- ตรวจสอบสิทธิ์ของ folder
- ตรวจสอบการตั้งค่า web server

### 4. Image Upload Issues
- ตรวจสอบ PHP extensions (GD)
- ตรวจสอบขนาดไฟล์ที่อนุญาต
- ตรวจสอบสิทธิ์ของ storage folder

## การอัปเดตระบบ

### 1. Backup ข้อมูล
```bash
# Backup database
mysqldump -u username -p soloshop > backup.sql

# Backup files
tar -czf backup.tar.gz /path/to/soloshop
```

### 2. Pull Updates
```bash
git pull origin main
composer install
npm install && npm run build
php artisan migrate
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## การสนับสนุน

หากพบปัญหาในการติดตั้งหรือใช้งาน กรุณาติดต่อทีมพัฒนา

---

**หมายเหตุ:** คู่มือนี้เหมาะสำหรับการติดตั้งบน Linux/Ubuntu Server
