# คู่มือการใช้งานระบบการตั้งค่าเว็บไซต์ SoloShop

## ภาพรวม

ระบบการตั้งค่าเว็บไซต์ช่วยให้ Admin สามารถจัดการข้อมูลพื้นฐานของเว็บไซต์ได้อย่างง่ายดาย รวมถึง:

- **ชื่อเว็บไซต์** - เปลี่ยนชื่อเว็บไซต์ได้ตามต้องการ (เช่น "ดอม", "ผู้ใหญ่บ้าน")
- **โลโก้เว็บไซต์** - อัปโหลดและจัดการโลโก้
- **Favicon** - ไอคอนที่แสดงในแท็บเบราว์เซอร์
- **ข้อมูลติดต่อ** - อีเมล, เบอร์โทร, ที่อยู่
- **โซเชียลมีเดีย** - ลิงก์ Facebook, Twitter, Instagram, Line

## การเข้าถึงระบบการตั้งค่า

### วิธีที่ 1: ผ่านเมนูด้านข้าง
1. เข้าสู่ระบบ Admin ที่ `/admin`
2. ดูที่เมนูด้านข้าง ส่วน "ระบบ"
3. คลิก "การตั้งค่าเว็บไซต์"

### วิธีที่ 2: ผ่านแดชบอร์ด
1. เข้าสู่ระบบ Admin ที่ `/admin`
2. ดูที่ส่วน "การดำเนินการด่วน"
3. คลิกปุ่ม "การตั้งค่าเว็บไซต์"

### วิธีที่ 3: เข้าโดยตรง
- ไปที่ URL: `/admin/settings`

## การใช้งานระบบการตั้งค่า

### 1. ข้อมูลพื้นฐาน

#### ชื่อเว็บไซต์
- **ฟิลด์**: ชื่อเว็บไซต์ (บังคับ)
- **ตัวอย่าง**: "ดอม", "ผู้ใหญ่บ้าน", "SoloShop"
- **ผลลัพธ์**: ชื่อจะแสดงใน:
  - Title ของหน้าเว็บ
  - Navbar ของหน้าบ้าน
  - Brand ของ Admin Panel

#### คำอธิบายเว็บไซต์
- **ฟิลด์**: คำอธิบายเว็บไซต์ (ไม่บังคับ)
- **ตัวอย่าง**: "ผู้เชี่ยวชาญด้านการพัฒนาเว็บไซต์และการตลาดดิจิทัล"
- **ผลลัพธ์**: ใช้สำหรับ SEO และการแสดงผล

### 2. รูปภาพ

#### โลโก้เว็บไซต์
- **รองรับไฟล์**: JPEG, JPG, PNG, GIF, WebP
- **ขนาดสูงสุด**: 2MB
- **ผลลัพธ์**: โลโก้จะแสดงใน:
  - Navbar ของหน้าบ้าน
  - Brand ของ Admin Panel
- **การลบ**: คลิกปุ่ม "ลบโลโก้" เพื่อลบโลโก้ปัจจุบัน

#### Favicon
- **รองรับไฟล์**: ICO, PNG, JPG
- **ขนาดสูงสุด**: 1MB
- **ผลลัพธ์**: แสดงในแท็บเบราว์เซอร์
- **การลบ**: คลิกปุ่ม "ลบ Favicon" เพื่อลบ favicon ปัจจุบัน

### 3. ข้อมูลติดต่อ

#### อีเมลติดต่อ
- **ฟิลด์**: อีเมลติดต่อ (ไม่บังคับ)
- **รูปแบบ**: ต้องเป็นอีเมลที่ถูกต้อง
- **ตัวอย่าง**: "<EMAIL>"

#### เบอร์โทรติดต่อ
- **ฟิลด์**: เบอร์โทรติดต่อ (ไม่บังคับ)
- **ตัวอย่าง**: "02-123-4567"

#### ที่อยู่
- **ฟิลด์**: ที่อยู่ (ไม่บังคับ)
- **ตัวอย่าง**: "123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110"

### 4. โซเชียลมีเดีย

#### Facebook URL
- **ฟิลด์**: ลิงก์ Facebook (ไม่บังคับ)
- **รูปแบบ**: ต้องเป็น URL ที่ถูกต้อง
- **ตัวอย่าง**: "https://facebook.com/soloshop"

#### Twitter URL
- **ฟิลด์**: ลิงก์ Twitter (ไม่บังคับ)
- **ตัวอย่าง**: "https://twitter.com/soloshop"

#### Instagram URL
- **ฟิลด์**: ลิงก์ Instagram (ไม่บังคับ)
- **ตัวอย่าง**: "https://instagram.com/soloshop"

#### Line URL
- **ฟิลด์**: ลิงก์ Line (ไม่บังคับ)
- **ตัวอย่าง**: "https://line.me/ti/p/@soloshop"

## การบันทึกการตั้งค่า

1. กรอกข้อมูลในฟอร์มตามต้องการ
2. อัปโหลดรูปภาพ (ถ้ามี)
3. คลิกปุ่ม "บันทึกการตั้งค่า"
4. ระบบจะแสดงข้อความยืนยันเมื่อบันทึกสำเร็จ
5. การเปลี่ยนแปลงจะมีผลทันทีทั่วทั้งเว็บไซต์

## ตัวอย่างการใช้งาน

### เปลี่ยนชื่อเว็บไซต์เป็น "ดอม"
1. เข้าไปที่การตั้งค่าเว็บไซต์
2. เปลี่ยน "ชื่อเว็บไซต์" จาก "SoloShop" เป็น "ดอม"
3. คลิก "บันทึกการตั้งค่า"
4. ชื่อเว็บไซต์จะเปลี่ยนเป็น "ดอม" ทั่วทั้งเว็บไซต์

### อัปโหลดโลโก้ใหม่
1. เข้าไปที่การตั้งค่าเว็บไซต์
2. ในส่วน "โลโก้เว็บไซต์" คลิก "Choose File"
3. เลือกไฟล์รูปภาพที่ต้องการ
4. คลิก "บันทึกการตั้งค่า"
5. โลโก้ใหม่จะแสดงในเว็บไซต์

## ข้อควรระวัง

1. **ขนาดไฟล์**: ตรวจสอบขนาดไฟล์ก่อนอัปโหลด
2. **รูปแบบไฟล์**: ใช้เฉพาะไฟล์ที่รองรับ
3. **ชื่อเว็บไซต์**: เป็นฟิลด์บังคับ ต้องกรอก
4. **URL โซเชียลมีเดีย**: ต้องเป็น URL ที่ถูกต้อง (เริ่มต้นด้วย http:// หรือ https://)

## การแก้ไขปัญหา

### ปัญหา: อัปโหลดรูปภาพไม่ได้
- ตรวจสอบขนาดไฟล์ (ไม่เกิน 2MB สำหรับโลโก้, 1MB สำหรับ favicon)
- ตรวจสอบรูปแบบไฟล์ที่รองรับ
- ตรวจสอบการตั้งค่า storage ของ Laravel

### ปัญหา: การเปลี่ยนแปลงไม่มีผล
- ลองรีเฟรชหน้าเว็บ
- ล้าง cache ของเบราว์เซอร์
- ตรวจสอบว่าบันทึกการตั้งค่าสำเร็จหรือไม่

### ปัญหา: ไม่สามารถเข้าถึงหน้าการตั้งค่าได้
- ตรวจสอบว่าเข้าสู่ระบบ Admin แล้ว
- ตรวจสอบสิทธิ์ Admin
- ตรวจสอบ URL ที่ถูกต้อง: `/admin/settings`

## ข้อมูลเทคนิค

- **Model**: `App\Models\SiteSetting`
- **Controller**: `App\Http\Controllers\SiteSettingController`
- **Routes**: `/admin/settings`
- **Views**: `resources/views/admin/settings/index.blade.php`
- **Migration**: `2025_07_13_120000_create_site_settings_table`
- **Seeder**: `Database\Seeders\SiteSettingSeeder`
