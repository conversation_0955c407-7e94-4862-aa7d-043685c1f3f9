# คำแนะนำการติดตั้งฐานข้อมูล SoloShop ใน XAMPP

## ขั้นตอนการติดตั้ง

### 1. เตรียม XAMPP
- ดาวน์โหลดและติดตั้ง XAMPP จาก https://www.apachefriends.org/
- เปิด XAMPP Control Panel
- เริ่มต้นบริการ Apache และ MySQL

### 2. นำเข้าฐานข้อมูล
1. เปิดเว็บเบราว์เซอร์และไปที่ `http://localhost/phpmyadmin`
2. คลิกแท็บ "Import" ในเมนูด้านบน
3. คลิก "Choose File" และเลือกไฟล์ `database/soloshop_database.sql`
4. คลิก "Go" เพื่อนำเข้าฐานข้อมูล
5. รอจนกระทั่งการนำเข้าเสร็จสิ้น

### 3. ตั้งค่า Laravel
1. คัดลอกไฟล์ `.env.example` เป็น `.env`
   ```bash
   copy .env.example .env
   ```

2. แก้ไขไฟล์ `.env` ให้ตรงกับการตั้งค่าฐานข้อมูล:
   ```
   APP_NAME=SoloShop
   APP_URL=http://localhost/SoloShop/public
   
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=soloshop
   DB_USERNAME=root
   DB_PASSWORD=
   ```

3. สร้าง Application Key:
   ```bash
   php artisan key:generate
   ```

### 4. ตั้งค่าโฟลเดอร์
1. วางโปรเจค SoloShop ในโฟลเดอร์ `C:\xampp\htdocs\`
2. ตั้งค่าสิทธิ์การเขียนไฟล์สำหรับโฟลเดอร์:
   - `storage/`
   - `bootstrap/cache/`

### 5. เข้าถึงเว็บไซต์
- เปิดเว็บเบราว์เซอร์และไปที่ `http://localhost/SoloShop/public`

## ข้อมูลการเข้าสู่ระบบ

### ผู้ดูแลระบบ (Admin)
- **อีเมล**: <EMAIL>
- **รหัสผ่าน**: password

### ผู้ใช้ทั่วไป
- **อีเมล**: <EMAIL>
- **รหัสผ่าน**: password

- **อีเมล**: <EMAIL>
- **รหัสผ่าน**: password

## โครงสร้างฐานข้อมูล

### ตารางหลัก
1. **users** - ข้อมูลผู้ใช้และผู้ดูแลระบบ
2. **services** - ข้อมูลบริการต่างๆ
3. **packages** - ข้อมูลแพ็กเกจบริการ
4. **articles** - ข้อมูลบทความ
5. **homepage_contents** - เนื้อหาหน้าแรก
6. **contacts** - ข้อมูลการติดต่อจากลูกค้า

### ตารางระบบ Laravel
- **migrations** - บันทึกการ migrate ฐานข้อมูล
- **password_resets** - การรีเซ็ตรหัสผ่าน
- **failed_jobs** - งานที่ล้มเหลว
- **personal_access_tokens** - โทเค็นการเข้าถึง API

## การแก้ไขปัญหาที่อาจเกิดขึ้น

### ปัญหาการเชื่อมต่อฐานข้อมูล
1. ตรวจสอบว่า MySQL ใน XAMPP ทำงานอยู่
2. ตรวจสอบการตั้งค่าในไฟล์ `.env`
3. ตรวจสอบว่าฐานข้อมูล `soloshop` ถูกสร้างแล้ว

### ปัญหา Permission
1. ตั้งค่าสิทธิ์การเขียนไฟล์สำหรับโฟลเดอร์ `storage` และ `bootstrap/cache`
2. ใน Windows: คลิกขวาโฟลเดอร์ → Properties → Security → แก้ไขสิทธิ์

### ปัญหา Application Key
1. รันคำสั่ง `php artisan key:generate`
2. ตรวจสอบว่าไฟล์ `.env` มี `APP_KEY` แล้ว

## ข้อมูลตัวอย่างที่มีในฐานข้อมูล

### บริการ (Services)
- การพัฒนาเว็บไซต์ (15,000 บาท)
- การออกแบบ UI/UX (8,000 บาท)
- การตลาดดิจิทัล (12,000 บาท)
- การพัฒนาแอปพลิเคชัน (25,000 บาท)

### แพ็กเกจ (Packages)
- แพ็กเกจเริ่มต้น (20,000 บาท)
- แพ็กเกจมาตรฐาน (35,000 บาท)
- แพ็กเกจพรีเมียม (60,000 บาท)

### บทความ (Articles)
- เทรนด์การพัฒนาเว็บไซต์ในปี 2025
- วิธีเลือกบริษัทพัฒนาเว็บไซต์ที่ดี
- ความสำคัญของ SEO สำหรับธุรกิจออนไลน์

### การติดต่อ (Contacts)
- ข้อมูลการติดต่อตัวอย่าง 3 รายการ

## หมายเหตุ
- ข้อมูลทั้งหมดเป็นข้อมูลตัวอย่างสำหรับการทดสอบระบบ
- สามารถแก้ไขหรือเพิ่มข้อมูลผ่านระบบ Admin ได้
- รูปภาพในฐานข้อมูลเป็นเพียงชื่อไฟล์ ต้องอัปโหลดรูปจริงผ่านระบบ
