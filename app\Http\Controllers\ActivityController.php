<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\ActivityCategory;
use Illuminate\Http\Request;

class ActivityController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = Activity::with(['category', 'images'])->published()->latest();
        
        // Filter by category if specified
        if ($request->has('category') && $request->category) {
            $query->where('category_id', $request->category);
        }
        
        $activities = $query->get();
        $categories = ActivityCategory::active()->get();
        
        return view('activities.index', compact('activities', 'categories'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Activity  $activity
     * @return \Illuminate\Http\Response
     */
    public function show(Activity $activity)
    {
        // Load relationships
        $activity->load(['category', 'images']);
        
        // Get related activities from the same category
        $relatedActivities = Activity::with(['category', 'images'])
            ->published()
            ->where('category_id', $activity->category_id)
            ->where('id', '!=', $activity->id)
            ->latest()
            ->limit(3)
            ->get();
        
        return view('activities.show', compact('activity', 'relatedActivities'));
    }
}
