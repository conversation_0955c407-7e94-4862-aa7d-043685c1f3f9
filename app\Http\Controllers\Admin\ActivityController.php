<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\ActivityCategory;
use App\Models\ActivityImage;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;

class ActivityController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $activities = Activity::with(['category', 'images'])->latest()->get();
        return view('admin.activities.index', compact('activities'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = ActivityCategory::active()->get();
        return view('admin.activities.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:activity_categories,id',
            'activity_date' => 'nullable|date',
            'location' => 'nullable|string|max:255',
            'is_published' => 'boolean',
            'cover_image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            'captions.*' => 'nullable|string|max:255',
        ]);

        $data['is_published'] = $request->has('is_published');

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            $imageErrors = ImageHelper::validateImage($request->file('cover_image'));
            if (!empty($imageErrors)) {
                return back()->withErrors(['cover_image' => $imageErrors])->withInput();
            }
            $data['cover_image'] = ImageHelper::uploadAndResize($request->file('cover_image'), 'activities');
        }

        $activity = Activity::create($data);

        // Handle gallery images
        if ($request->hasFile('gallery_images')) {
            foreach ($request->file('gallery_images') as $index => $image) {
                $imageErrors = ImageHelper::validateImage($image);
                if (empty($imageErrors)) {
                    $imagePath = ImageHelper::uploadAndResize($image, 'activities/gallery');
                    ActivityImage::create([
                        'activity_id' => $activity->id,
                        'image_path' => $imagePath,
                        'caption' => $request->captions[$index] ?? null,
                        'sort_order' => $index,
                    ]);
                }
            }
        }

        return redirect()->route('admin.activities.index')->with('success', 'เพิ่มกิจกรรมสำเร็จ');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Activity $activity)
    {
        $activity->load(['category', 'images']);
        $categories = ActivityCategory::active()->get();
        return view('admin.activities.edit', compact('activity', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Activity $activity)
    {
        $data = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:activity_categories,id',
            'activity_date' => 'nullable|date',
            'location' => 'nullable|string|max:255',
            'is_published' => 'boolean',
            'cover_image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            'captions.*' => 'nullable|string|max:255',
        ]);

        $data['is_published'] = $request->has('is_published');

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            $imageErrors = ImageHelper::validateImage($request->file('cover_image'));
            if (!empty($imageErrors)) {
                return back()->withErrors(['cover_image' => $imageErrors])->withInput();
            }

            // Delete old cover image
            ImageHelper::deleteImage($activity->cover_image);

            // Upload new cover image
            $data['cover_image'] = ImageHelper::uploadAndResize($request->file('cover_image'), 'activities');
        }

        $activity->update($data);

        // Handle gallery images
        if ($request->hasFile('gallery_images')) {
            foreach ($request->file('gallery_images') as $index => $image) {
                $imageErrors = ImageHelper::validateImage($image);
                if (empty($imageErrors)) {
                    $imagePath = ImageHelper::uploadAndResize($image, 'activities/gallery');
                    ActivityImage::create([
                        'activity_id' => $activity->id,
                        'image_path' => $imagePath,
                        'caption' => $request->captions[$index] ?? null,
                        'sort_order' => $activity->images()->count(),
                    ]);
                }
            }
        }

        return redirect()->route('admin.activities.index')->with('success', 'อัปเดตกิจกรรมสำเร็จ');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Activity $activity)
    {
        // Delete cover image
        ImageHelper::deleteImage($activity->cover_image);

        // Delete gallery images
        foreach ($activity->images as $image) {
            ImageHelper::deleteImage($image->image_path);
        }

        $activity->delete();

        return redirect()->route('admin.activities.index')->with('success', 'ลบกิจกรรมสำเร็จ');
    }

    /**
     * Delete a specific gallery image
     */
    public function deleteImage(ActivityImage $image)
    {
        ImageHelper::deleteImage($image->image_path);
        $image->delete();

        return response()->json(['success' => true]);
    }

    /**
     * Update image caption
     */
    public function updateImageCaption(Request $request, ActivityImage $image)
    {
        $request->validate([
            'caption' => 'nullable|string|max:255',
        ]);

        $image->update([
            'caption' => $request->caption
        ]);

        return response()->json([
            'success' => true,
            'message' => 'อัปเดตคำบรรยายสำเร็จ'
        ]);
    }

    /**
     * Update images sort order
     */
    public function updateImageOrder(Request $request, Activity $activity)
    {
        $request->validate([
            'image_ids' => 'required|array',
            'image_ids.*' => 'exists:activity_images,id'
        ]);

        foreach ($request->image_ids as $index => $imageId) {
            ActivityImage::where('id', $imageId)
                ->where('activity_id', $activity->id)
                ->update(['sort_order' => $index]);
        }

        return response()->json([
            'success' => true,
            'message' => 'จัดเรียงรูปภาพสำเร็จ'
        ]);
    }

    /**
     * Replace an existing image
     */
    public function replaceImage(Request $request, ActivityImage $image)
    {
        $request->validate([
            'new_image' => 'required|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            'caption' => 'nullable|string|max:255',
        ]);

        $imageErrors = ImageHelper::validateImage($request->file('new_image'));
        if (!empty($imageErrors)) {
            return response()->json([
                'success' => false,
                'message' => implode(', ', $imageErrors)
            ], 422);
        }

        // Delete old image
        ImageHelper::deleteImage($image->image_path);

        // Upload new image
        $newImagePath = ImageHelper::uploadAndResize($request->file('new_image'), 'activities/gallery');

        // Update image record
        $image->update([
            'image_path' => $newImagePath,
            'caption' => $request->caption ?? $image->caption
        ]);

        return response()->json([
            'success' => true,
            'message' => 'เปลี่ยนรูปภาพสำเร็จ',
            'new_image_url' => ImageHelper::getImageUrl($newImagePath)
        ]);
    }
}
