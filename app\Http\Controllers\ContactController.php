<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // สำหรับหน้าบ้าน
        if (!$request->is('admin/*')) {
            return view('contact');
        }

        // สำหรับหลังบ้าน - Admin
        $query = Contact::latest();

        // กรองตามสถานะ
        if ($request->get('filter') === 'unread') {
            $query->unread();
        } elseif ($request->get('filter') === 'read') {
            $query->read();
        }

        $contacts = $query->get();
        return view('admin.contacts.index', compact('contacts'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.contacts.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'address' => 'nullable|string',
            'message' => 'required|string',
        ]);
        
        \App\Models\Contact::create($data);
        
        return redirect()->route('contact.index')->with('success', 'ส่งข้อความเรียบร้อยแล้ว เราจะติดต่อกลับโดยเร็วที่สุด');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Contact  $contact
     * @return \Illuminate\Http\Response
     */
    public function show(Contact $contact)
    {
        return view('admin.contacts.show', compact('contact'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Contact  $contact
     * @return \Illuminate\Http\Response
     */
    public function edit(Contact $contact)
    {
        // ทำเครื่องหมายว่าอ่านแล้วเมื่อเปิดดู
        if (!$contact->is_read) {
            $contact->markAsRead();
        }

        return view('admin.contacts.edit', compact('contact'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Contact  $contact
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Contact $contact)
    {
        // ถ้าเป็นการอัปเดตสถานะการอ่าน
        if ($request->has('is_read')) {
            $contact->update(['is_read' => $request->boolean('is_read')]);
            return redirect()->route('admin.contacts.index')->with('success', 'อัปเดตสถานะสำเร็จ');
        }

        // ถ้าเป็นการอัปเดตข้อมูลทั่วไป
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'message' => 'nullable|string',
        ]);

        $contact->update($data);
        return redirect()->route('admin.contacts.index')->with('success', 'อัปเดตข้อมูลติดต่อสำเร็จ');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Contact  $contact
     * @return \Illuminate\Http\Response
     */
    public function destroy(Contact $contact)
    {
        $contact->delete();
        return redirect()->route('admin.contacts.index')->with('success', 'ลบข้อมูลติดต่อสำเร็จ');
    }
}
