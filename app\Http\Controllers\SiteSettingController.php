<?php

namespace App\Http\Controllers;

use App\Models\SiteSetting;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SiteSettingController extends Controller
{
    /**
     * Display the site settings form.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $settings = SiteSetting::getSettings();
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update the site settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_description' => 'nullable|string',
            'hero_title' => 'nullable|string|max:255',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:255',
            'contact_address' => 'nullable|string',
            'facebook_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'line_url' => 'nullable|url|max:255',
            'site_logo' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            'site_favicon' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp,ico|max:1024',
            'hero_icon' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ]);

        $settings = SiteSetting::getSettings();
        
        // Handle logo upload
        if ($request->hasFile('site_logo')) {
            // Delete old logo if exists
            if ($settings->site_logo && Storage::disk('public')->exists($settings->site_logo)) {
                Storage::disk('public')->delete($settings->site_logo);
            }

            $logoPath = ImageHelper::uploadAndResize($request->file('site_logo'), 'settings');
            $settings->site_logo = $logoPath;
        }

        // Handle favicon upload
        if ($request->hasFile('site_favicon')) {
            // Delete old favicon if exists
            if ($settings->site_favicon && Storage::disk('public')->exists($settings->site_favicon)) {
                Storage::disk('public')->delete($settings->site_favicon);
            }

            $faviconPath = ImageHelper::uploadAndResize($request->file('site_favicon'), 'settings');
            $settings->site_favicon = $faviconPath;
        }

        // Handle hero icon upload
        if ($request->hasFile('hero_icon')) {
            // Delete old hero icon if exists
            if ($settings->hero_icon && Storage::disk('public')->exists($settings->hero_icon)) {
                Storage::disk('public')->delete($settings->hero_icon);
            }

            $heroIconPath = ImageHelper::uploadAndResize($request->file('hero_icon'), 'settings');
            $settings->hero_icon = $heroIconPath;
        }

        // Update other fields
        $settings->update($request->only([
            'site_name',
            'site_description',
            'hero_title',
            'contact_email',
            'contact_phone',
            'contact_address',
            'facebook_url',
            'twitter_url',
            'instagram_url',
            'line_url',
        ]));

        return redirect()->route('admin.settings.index')
            ->with('success', 'การตั้งค่าเว็บไซต์ได้รับการอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * Remove logo image
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function removeLogo(Request $request)
    {
        $settings = SiteSetting::getSettings();

        if ($settings->site_logo && Storage::disk('public')->exists($settings->site_logo)) {
            Storage::disk('public')->delete($settings->site_logo);
        }

        $settings->update(['site_logo' => null]);

        return response()->json(['success' => true, 'message' => 'โลโก้ถูกลบเรียบร้อยแล้ว']);
    }

    /**
     * Remove hero icon image
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function removeHeroIcon(Request $request)
    {
        $settings = SiteSetting::getSettings();

        if ($settings->hero_icon && Storage::disk('public')->exists($settings->hero_icon)) {
            Storage::disk('public')->delete($settings->hero_icon);
        }

        $settings->update(['hero_icon' => null]);

        return response()->json(['success' => true, 'message' => 'ไอคอนหลักถูกลบเรียบร้อยแล้ว']);
    }

    /**
     * Remove favicon image
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function removeFavicon(Request $request)
    {
        $settings = SiteSetting::getSettings();
        
        if ($settings->site_favicon && Storage::disk('public')->exists($settings->site_favicon)) {
            Storage::disk('public')->delete($settings->site_favicon);
        }
        
        $settings->update(['site_favicon' => null]);
        
        return response()->json(['success' => true, 'message' => 'Favicon ถูกลบเรียบร้อยแล้ว']);
    }
}
