<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckFileUpload
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if request has file uploads
        if ($request->hasFile('image')) {
            $file = $request->file('image');
            
            // Check if upload was successful
            if (!$file->isValid()) {
                return back()->withErrors(['image' => 'การอัปโหลดไฟล์ล้มเหลว กรุณาลองใหม่อีกครั้ง'])->withInput();
            }
            
            // Check file size (2MB = 2048KB)
            if ($file->getSize() > 2048 * 1024) {
                return back()->withErrors(['image' => 'ขนาดไฟล์ต้องไม่เกิน 2MB'])->withInput();
            }
            
            // Check file type
            $allowedTypes = ['jpeg', 'jpg', 'png', 'gif', 'webp'];
            $extension = strtolower($file->getClientOriginalExtension());
            
            if (!in_array($extension, $allowedTypes)) {
                return back()->withErrors(['image' => 'ไฟล์ต้องเป็นรูปภาพ (JPEG, JPG, PNG, GIF, WebP)'])->withInput();
            }
        }
        
        return $next($request);
    }
}
