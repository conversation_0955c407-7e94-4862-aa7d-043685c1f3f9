<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Activity extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'category_id',
        'cover_image',
        'activity_date',
        'location',
        'is_published',
    ];

    protected $casts = [
        'activity_date' => 'date',
        'is_published' => 'boolean',
    ];

    /**
     * Get the category that owns the activity.
     */
    public function category()
    {
        return $this->belongsTo(ActivityCategory::class, 'category_id');
    }

    /**
     * Get the images for the activity.
     */
    public function images()
    {
        return $this->hasMany(ActivityImage::class)->orderBy('sort_order');
    }

    /**
     * Scope a query to only include published activities.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope a query to order by activity date descending.
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('activity_date', 'desc')->orderBy('created_at', 'desc');
    }
}
