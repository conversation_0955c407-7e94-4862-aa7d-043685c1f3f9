<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ActivityImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'activity_id',
        'image_path',
        'caption',
        'sort_order',
    ];

    /**
     * Get the activity that owns the image.
     */
    public function activity()
    {
        return $this->belongsTo(Activity::class);
    }
}
