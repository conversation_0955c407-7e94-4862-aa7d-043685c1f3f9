<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SiteSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'site_name',
        'site_description',
        'site_logo',
        'site_favicon',
        'hero_icon',
        'hero_title',
        'contact_email',
        'contact_phone',
        'contact_address',
        'facebook_url',
        'twitter_url',
        'instagram_url',
        'line_url',
    ];

    /**
     * Get the site settings (singleton pattern)
     * 
     * @return SiteSetting
     */
    public static function getSettings()
    {
        $settings = self::first();
        
        if (!$settings) {
            $settings = self::create([
                'site_name' => 'SoloShop',
                'site_description' => 'ผู้เชี่ยวชาญด้านการพัฒนาเว็บไซต์และการตลาดดิจิทัล',
                'hero_title' => 'ยินดีต้อนรับสู่ SoloShop',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '02-123-4567',
                'contact_address' => '123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110',
            ]);
        }
        
        return $settings;
    }

    /**
     * Get site logo URL
     * 
     * @return string
     */
    public function getLogoUrlAttribute()
    {
        if ($this->site_logo) {
            return asset('storage/' . $this->site_logo);
        }
        return asset('images/default-logo.png');
    }

    /**
     * Get site favicon URL
     * 
     * @return string
     */
    public function getFaviconUrlAttribute()
    {
        if ($this->site_favicon) {
            return asset('storage/' . $this->site_favicon);
        }
        return asset('favicon.ico');
    }
}
