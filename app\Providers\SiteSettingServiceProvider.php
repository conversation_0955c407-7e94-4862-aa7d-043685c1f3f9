<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\SiteSetting;

class SiteSettingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Share site settings with all views
        View::composer('*', function ($view) {
            try {
                $siteSettings = SiteSetting::getSettings();
                $view->with('siteSettings', $siteSettings);
            } catch (\Exception $e) {
                // If database is not ready or table doesn't exist, use default values
                $defaultSettings = (object) [
                    'site_name' => 'SoloShop',
                    'site_description' => 'ผู้เชี่ยวชาญด้านการพัฒนาเว็บไซต์และการตลาดดิจิทัล',
                    'site_logo' => null,
                    'site_favicon' => null,
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '02-123-4567',
                    'contact_address' => '123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110',
                    'facebook_url' => null,
                    'twitter_url' => null,
                    'instagram_url' => null,
                    'line_url' => null,
                ];
                $view->with('siteSettings', $defaultSettings);
            }
        });
    }
}
