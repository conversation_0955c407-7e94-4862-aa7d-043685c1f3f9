<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('site_settings', function (Blueprint $table) {
            $table->string('hero_icon')->nullable()->after('site_favicon'); // ไอคอนหลักของ hero section
            $table->text('hero_title')->nullable()->after('hero_icon'); // ข้อความหลักของ hero section
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('site_settings', function (Blueprint $table) {
            $table->dropColumn(['hero_icon', 'hero_title']);
        });
    }
};
