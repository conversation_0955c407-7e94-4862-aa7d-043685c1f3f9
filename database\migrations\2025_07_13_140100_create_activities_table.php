<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('activities', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // หัวข้อกิจกรรม
            $table->text('description'); // คำอธิบายกิจกรรม
            $table->foreignId('category_id')->constrained('activity_categories')->onDelete('cascade'); // หมวดหมู่
            $table->string('cover_image')->nullable(); // รูปภาพหน้าปก
            $table->date('activity_date')->nullable(); // วันที่จัดกิจกรรม
            $table->string('location')->nullable(); // สถานที่
            $table->boolean('is_published')->default(true); // สถานะการเผยแพร่
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('activities');
    }
};
