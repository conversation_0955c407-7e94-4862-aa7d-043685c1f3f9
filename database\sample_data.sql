-- ===================================================================
-- ข้อมูลตัวอย่างเพิ่มเติมสำหรับ SoloShop
-- ===================================================================
-- ไฟล์นี้ใช้สำหรับเพิ่มข้อมูลตัวอย่างเพิ่มเติม
-- รันหลังจากนำเข้าฐานข้อมูลหลักแล้ว
-- ===================================================================

USE `soloshop`;

-- ===================================================================
-- เพิ่มผู้ใช้เพิ่มเติม
-- ===================================================================

INSERT INTO `users` (`name`, `email`, `is_admin`, `email_verified_at`, `password`, `created_at`, `updated_at`) VALUES
('สมศักดิ์ ผู้จัดการ', '<EMAIL>', 1, NOW(), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW()),
('นางสาวพิมพ์ใส ลูกค้า', '<EMAIL>', 0, NOW(), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW()),
('คุณวิทยา ธุรกิจ', '<EMAIL>', 0, NOW(), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW());

-- ===================================================================
-- เพิ่มบริการเพิ่มเติม
-- ===================================================================

INSERT INTO `services` (`title`, `description`, `image`, `price`, `created_at`, `updated_at`) VALUES
('การปรับปรุง SEO', 'บริการปรับปรุงเว็บไซต์ให้ติดอันดับการค้นหาใน Google', 'services/seo-optimization.jpg', 6000.00, NOW(), NOW()),
('การดูแลเว็บไซต์', 'บริการดูแลและบำรุงรักษาเว็บไซต์รายเดือน', 'services/website-maintenance.jpg', 3000.00, NOW(), NOW()),
('การสร้างร้านค้าออนไลน์', 'บริการสร้างร้านค้าออนไลน์พร้อมระบบชำระเงิน', 'services/ecommerce.jpg', 30000.00, NOW(), NOW()),
('การฝึกอบรมดิจิทัล', 'บริการฝึกอบรมการใช้งานเครื่องมือดิจิทัล', 'services/digital-training.jpg', 5000.00, NOW(), NOW());

-- ===================================================================
-- เพิ่มแพ็กเกจเพิ่มเติม
-- ===================================================================

INSERT INTO `packages` (`name`, `description`, `price`, `image`, `created_at`, `updated_at`) VALUES
('แพ็กเกจร้านค้าออนไลน์', 'เหมาะสำหรับธุรกิจที่ต้องการขายสินค้าออนไลน์ ประกอบด้วยเว็บไซต์ e-commerce และระบบจัดการสินค้า', 45000.00, 'packages/ecommerce-package.jpg', NOW(), NOW()),
('แพ็กเกจโรงแรม', 'เหมาะสำหรับธุรกิจโรงแรมและรีสอร์ท ประกอบด้วยเว็บไซต์จองห้องและระบบจัดการ', 55000.00, 'packages/hotel-package.jpg', NOW(), NOW()),
('แพ็กเกจร้านอาหาร', 'เหมาะสำหรับร้านอาหารและคาเฟ่ ประกอบด้วยเว็บไซต์และระบบสั่งอาหารออนไลน์', 25000.00, 'packages/restaurant-package.jpg', NOW(), NOW());

-- ===================================================================
-- เพิ่มบทความเพิ่มเติม
-- ===================================================================

INSERT INTO `articles` (`title`, `content`, `image`, `published_at`, `created_at`, `updated_at`) VALUES
('การออกแบบเว็บไซต์ที่ดึงดูดลูกค้า', 'การออกแบบเว็บไซต์ที่ดีต้องคำนึงถึงประสบการณ์ผู้ใช้ (UX) และการออกแบบที่สวยงาม (UI) เพื่อให้ลูกค้าเกิดความประทับใจและต้องการใช้บริการ', 'articles/attractive-web-design.jpg', NOW(), NOW(), NOW()),
('เทคนิคการตลาดออนไลน์ที่มีประสิทธิภาพ', 'การตลาดออนไลน์ในยุคปัจจุบันต้องใช้หลากหลายช่องทาง เช่น Social Media, Google Ads, Email Marketing และ Content Marketing', 'articles/online-marketing-tips.jpg', NOW(), NOW(), NOW()),
('ความปลอดภัยของเว็บไซต์', 'การรักษาความปลอดภัยของเว็บไซต์เป็นสิ่งสำคัญ ต้องมีการอัปเดตระบบ ใช้ SSL Certificate และมีระบบสำรองข้อมูล', 'articles/website-security.jpg', NOW(), NOW(), NOW()),
('การใช้ Social Media เพื่อธุรกิจ', 'Social Media เป็นเครื่องมือสำคัญในการสร้างความสัมพันธ์กับลูกค้า เพิ่มการรับรู้แบรนด์ และเพิ่มยอดขาย', 'articles/social-media-business.jpg', NOW(), NOW(), NOW());

-- ===================================================================
-- เพิ่มเนื้อหาหน้าแรกเพิ่มเติม
-- ===================================================================

INSERT INTO `homepage_contents` (`section`, `title`, `content`, `image`, `button_text`, `button_link`, `created_at`, `updated_at`) VALUES
('testimonials', 'ความคิดเห็นจากลูกค้า', 'ลูกค้าของเรามีความพึงพอใจในบริการและผลงานที่เราส่งมอบ', 'homepage/testimonials.jpg', 'ดูรีวิวทั้งหมด', '/testimonials', NOW(), NOW()),
('portfolio', 'ผลงานของเรา', 'ชมผลงานการพัฒนาเว็บไซต์และการตลาดดิจิทัลที่เราได้ทำให้กับลูกค้า', 'homepage/portfolio.jpg', 'ดูผลงาน', '/portfolio', NOW(), NOW()),
('team', 'ทีมงานของเรา', 'ทีมงานมืออาชีพที่มีประสบการณ์และความเชี่ยวชาญในแต่ละสาขา', 'homepage/our-team.jpg', 'รู้จักทีมงาน', '/team', NOW(), NOW());

-- ===================================================================
-- เพิ่มการติดต่อเพิ่มเติม
-- ===================================================================

INSERT INTO `contacts` (`name`, `phone`, `email`, `address`, `message`, `is_read`, `created_at`, `updated_at`) VALUES
('นายประยุทธ์ ธุรกิจใหม่', '************', '<EMAIL>', '321 ถนนวิภาวดี กรุงเทพฯ 10900', 'ต้องการพัฒนาเว็บไซต์สำหรับธุรกิจใหม่ งบประมาณประมาณ 50,000 บาท', 0, NOW(), NOW()),
('นางสาวมาลี ร้านดอกไม้', '************', '<EMAIL>', '654 ถนนเจริญกรุง กรุงเทพฯ 10500', 'สนใจแพ็กเกจร้านค้าออนไลน์สำหรับขายดอกไม้', 0, NOW(), NOW()),
('คุณสุรชัย โรงแรม', '************', '<EMAIL>', '987 ถนนบีชรอด พัทยา 20150', 'ต้องการปรึกษาเรื่องระบบจองห้องออนไลน์', 1, NOW(), NOW()),
('นางสาวนิตยา คาเฟ่', '081-999-0000', '<EMAIL>', '147 ถนนนิมมานเหมินท์ เชียงใหม่ 50200', 'สนใจบริการการตลาดดิจิทัลสำหรับคาเฟ่', 0, NOW(), NOW()),
('คุณอนุชา ร้านเสื้อผ้า', '089-222-3333', '<EMAIL>', '258 ถนนสีลม กรุงเทพฯ 10500', 'ต้องการสร้างเว็บไซต์ขายเสื้อผ้าออนไลน์', 0, NOW(), NOW());

-- ===================================================================
-- คำสั่งสำหรับตรวจสอบข้อมูล
-- ===================================================================

/*
-- ตรวจสอบจำนวนข้อมูลในแต่ละตาราง
SELECT 'users' as table_name, COUNT(*) as total_records FROM users
UNION ALL
SELECT 'services', COUNT(*) FROM services
UNION ALL
SELECT 'packages', COUNT(*) FROM packages
UNION ALL
SELECT 'articles', COUNT(*) FROM articles
UNION ALL
SELECT 'homepage_contents', COUNT(*) FROM homepage_contents
UNION ALL
SELECT 'contacts', COUNT(*) FROM contacts;

-- ตรวจสอบผู้ใช้ที่เป็น admin
SELECT name, email, is_admin FROM users WHERE is_admin = 1;

-- ตรวจสอบการติดต่อที่ยังไม่ได้อ่าน
SELECT name, email, message, created_at FROM contacts WHERE is_read = 0;
*/
