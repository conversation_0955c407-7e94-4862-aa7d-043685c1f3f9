<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ActivityCategory;

class ActivityCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'งานบุญ',
                'description' => 'กิจกรรมงานบุญต่างๆ ที่จัดขึ้นในชุมชน',
                'color' => '#28a745',
                'is_active' => true,
            ],
            [
                'name' => 'งานศพ',
                'description' => 'กิจกรรมและพิธีกรรมที่เกี่ยวข้องกับงานศพ',
                'color' => '#6c757d',
                'is_active' => true,
            ],
            [
                'name' => 'กิจกรรมชุมชน',
                'description' => 'กิจกรรมต่างๆ ที่จัดขึ้นเพื่อชุมชน',
                'color' => '#17a2b8',
                'is_active' => true,
            ],
            [
                'name' => 'งานเทศกาล',
                'description' => 'งานเทศกาลประจำปีและงานพิเศษต่างๆ',
                'color' => '#ffc107',
                'is_active' => true,
            ],
            [
                'name' => 'งานพิเศษ',
                'description' => 'งานพิเศษและกิจกรรมเฉพาะกิจ',
                'color' => '#dc3545',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            ActivityCategory::create($category);
        }
    }
}
