<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Activity;
use App\Models\ActivityCategory;
use App\Models\ActivityImage;
use Illuminate\Support\Carbon;

class ActivitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        // Get categories
        $categories = ActivityCategory::all();
        
        if ($categories->isEmpty()) {
            $this->command->info('กรุณารัน ActivityCategorySeeder ก่อน');
            return;
        }

        $activities = [
            [
                'title' => 'งานบุญประจำปี 2567',
                'description' => 'งานบุญประจำปีที่จัดขึ้นเพื่อสร้างความสามัคคีในชุมชน มีการทำบุญตักบาตร การแสดงพื้นบ้าน และกิจกรรมต่างๆ มากมาย',
                'category_id' => $categories->where('name', 'งานบุญ')->first()->id,
                'cover_image' => 'activities/boon-festival-2567.jpg',
                'activity_date' => Carbon::now()->subDays(30),
                'location' => 'วัดพระแก้ว',
                'is_published' => true,
            ],
            [
                'title' => 'พิธีสวดอภิธรรม',
                'description' => 'พิธีสวดอภิธรรมเพื่อส่งกุศลให้แก่ผู้ล่วงลับ จัดขึ้นด้วยความเคารพและศรัทธา',
                'category_id' => $categories->where('name', 'งานศพ')->first()->id,
                'cover_image' => 'activities/abhidhamma-ceremony.jpg',
                'activity_date' => Carbon::now()->subDays(15),
                'location' => 'ศาลาการเปรียญ',
                'is_published' => true,
            ],
            [
                'title' => 'กิจกรรมทำความสะอาดชุมชน',
                'description' => 'กิจกรรมร่วมมือกันทำความสะอาดชุมชน เพื่อสร้างสภาพแวดล้อมที่ดีให้กับทุกคนในชุมชน',
                'category_id' => $categories->where('name', 'กิจกรรมชุมชน')->first()->id,
                'cover_image' => 'activities/community-cleaning.jpg',
                'activity_date' => Carbon::now()->subDays(7),
                'location' => 'ชุมชนบ้านสวน',
                'is_published' => true,
            ],
            [
                'title' => 'เทศกาลสงกรานต์ 2567',
                'description' => 'งานเทศกาลสงกรานต์ประจำปี มีการรดน้ำดำหัวผู้สูงอายุ การแสดงดนตรีพื้นบ้าน และกิจกรรมสนุกสนานมากมาย',
                'category_id' => $categories->where('name', 'งานเทศกาล')->first()->id,
                'cover_image' => 'activities/songkran-festival-2567.jpg',
                'activity_date' => Carbon::create(2024, 4, 13),
                'location' => 'ลานวัดใหญ่',
                'is_published' => true,
            ],
            [
                'title' => 'งานบรรพชาสามเณร',
                'description' => 'พิธีบรรพชาสามเณรเพื่อสืบทอดพระพุทธศาสนา เป็นงานพิเศษที่มีความศักดิ์สิทธิ์และมีความหมาย',
                'category_id' => $categories->where('name', 'งานพิเศษ')->first()->id,
                'cover_image' => 'activities/novice-ordination.jpg',
                'activity_date' => Carbon::now()->subDays(45),
                'location' => 'พระอุโบสถ',
                'is_published' => true,
            ],
        ];

        foreach ($activities as $activityData) {
            $activity = Activity::create($activityData);
            
            // Create sample gallery images for each activity
            for ($i = 1; $i <= 3; $i++) {
                ActivityImage::create([
                    'activity_id' => $activity->id,
                    'image_path' => 'activities/gallery/' . $activity->id . '_' . $i . '.jpg',
                    'caption' => 'ภาพกิจกรรม ' . $activity->title . ' ภาพที่ ' . $i,
                    'sort_order' => $i - 1,
                ]);
            }
        }
    }
}
