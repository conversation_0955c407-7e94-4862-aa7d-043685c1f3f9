<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Contact;

class ContactSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        Contact::create([
            'name' => 'ผู้ใหญ่จากบริการ',
            'phone' => '************',
            'email' => '<EMAIL>',
            'address' => '123 หมู่ 4 ต.ตัวอย่าง อ.เมือง จ.กรุงเทพฯ',
            'message' => 'ติดต่อสอบถามบริการได้ตลอด 24 ชั่วโมง',
        ]);
    }
}
