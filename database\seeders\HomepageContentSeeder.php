<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\HomepageContent;

class HomepageContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        HomepageContent::create([
            'section' => 'hero',
            'title' => 'ยินดีต้อนรับสู่ SoloShop',
            'content' => 'แพลตฟอร์มการขายออนไลน์ที่ครบครันสำหรับธุรกิจของคุณ พร้อมระบบจัดการที่ใช้งานง่าย',
            'image' => null,
            'button_text' => 'เริ่มต้นใช้งาน',
            'button_link' => '/services',
        ]);
        HomepageContent::create([
            'section' => 'about',
            'title' => 'เกี่ยวกับเรา',
            'content' => 'เราคือทีมงานมืออาชีพด้านการพัฒนาระบบขายออนไลน์ พร้อมให้บริการและสนับสนุนธุรกิจของคุณให้เติบโตอย่างยั่งยืน',
            'image' => null,
            'button_text' => 'ติดต่อเรา',
            'button_link' => '/contact',
        ]);
        HomepageContent::create([
            'section' => 'features',
            'title' => 'คุณสมบัติเด่น',
            'content' => 'ระบบขายออนไลน์ที่ครบครัน รองรับการชำระเงินหลากหลายช่องทาง พร้อมระบบจัดการสินค้าและคำสั่งซื้อที่ทันสมัย',
            'image' => null,
            'button_text' => 'ดูแพ็คเกจ',
            'button_link' => '/packages',
        ]);
    }
}
