<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Package;

class PackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        Package::create([
            'name' => 'แพ็กเกจประหยัด',
            'description' => 'เหมาะสำหรับงานเล็ก งบประมาณประหยัด',
            'price' => 25000,
            'image' => null,
        ]);
        Package::create([
            'name' => 'แพ็กเกจพรีเมียม',
            'description' => 'ครบทุกบริการ พร้อมดอกไม้และอาหารระดับพรีเมียม',
            'price' => 90000,
            'image' => null,
        ]);
    }
}
