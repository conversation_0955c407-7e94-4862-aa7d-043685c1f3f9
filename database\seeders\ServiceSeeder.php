<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Service;

class ServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        Service::create([
            'title' => 'บริการจัดงานศพครบวงจร',
            'description' => 'บริการจัดงานศพแบบครบวงจร ทั้งพิธีทางศาสนา ดอกไม้ อาหาร และอุปกรณ์',
            'price' => 50000,
            'image' => null,
        ]);
        Service::create([
            'title' => 'บริการรถรับส่งศพ',
            'description' => 'บริการรถรับส่งศพพร้อมพนักงานดูแล',
            'price' => 8000,
            'image' => null,
        ]);
        Service::create([
            'title' => 'บริการจัดดอกไม้',
            'description' => 'บริการจัดดอกไม้สำหรับงานศพ พวงหรีด พวงมาลา',
            'price' => 3000,
            'image' => null,
        ]);
        Service::create([
            'title' => 'บริการจัดอาหาร',
            'description' => 'บริการจัดอาหารสำหรับงานศพ อาหารคาว หวาน เครื่องดื่ม',
            'price' => 15000,
            'image' => null,
        ]);
    }
}
