<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SiteSetting;

class SiteSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        SiteSetting::create([
            'site_name' => 'SoloShop',
            'site_description' => 'ผู้เชี่ยวชาญด้านการพัฒนาเว็บไซต์และการตลาดดิจิทัล',
            'hero_title' => 'ยินดีต้อนรับสู่ SoloShop',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '02-123-4567',
            'contact_address' => '123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110',
            'facebook_url' => 'https://facebook.com/soloshop',
            'twitter_url' => 'https://twitter.com/soloshop',
            'instagram_url' => 'https://instagram.com/soloshop',
            'line_url' => 'https://line.me/ti/p/@soloshop',
        ]);

        $this->command->info('Site settings created successfully!');
    }
}
