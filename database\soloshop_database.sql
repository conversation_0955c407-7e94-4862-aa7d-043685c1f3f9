-- ===================================================================
-- SoloShop Database Setup for MySQL/XAMPP
-- ===================================================================
-- สร้างฐานข้อมูลสำหรับระบบ SoloShop
-- วันที่สร้าง: 2025-07-13
-- ===================================================================

-- สร้างฐานข้อมูล
DROP DATABASE IF EXISTS `soloshop`;
CREATE DATABASE `soloshop` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `soloshop`;

-- ===================================================================
-- ตารางสำหรับ Laravel Migration Tracking
-- ===================================================================

CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================================================
-- ตารางผู้ใช้ (Users)
-- ===================================================================

CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `is_admin` tinyint(1) NOT NULL DEFAULT 0,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================================================
-- ตารางรีเซ็ตรหัสผ่าน (Password Resets)
-- ===================================================================

CREATE TABLE `password_resets` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================================================
-- ตารางงานที่ล้มเหลว (Failed Jobs)
-- ===================================================================

CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================================================
-- ตารางโทเค็นการเข้าถึงส่วนตัว (Personal Access Tokens)
-- ===================================================================

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================================================
-- ตารางบริการ (Services)
-- ===================================================================

CREATE TABLE `services` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================================================
-- ตารางแพ็กเกจ (Packages)
-- ===================================================================

CREATE TABLE `packages` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================================================
-- ตารางบทความ (Articles)
-- ===================================================================

CREATE TABLE `articles` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================================================
-- ตารางเนื้อหาหน้าแรก (Homepage Contents)
-- ===================================================================

CREATE TABLE `homepage_contents` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `section` varchar(255) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `content` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `button_text` varchar(255) DEFAULT NULL,
  `button_link` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================================================
-- ตารางการติดต่อ (Contacts)
-- ===================================================================

CREATE TABLE `contacts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `message` text DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================================================
-- ตารางการตั้งค่าเว็บไซต์ (Site Settings)
-- ===================================================================

CREATE TABLE `site_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `site_name` varchar(255) NOT NULL DEFAULT 'SoloShop',
  `site_description` text DEFAULT NULL,
  `site_logo` varchar(255) DEFAULT NULL,
  `site_favicon` varchar(255) DEFAULT NULL,
  `hero_icon` varchar(255) DEFAULT NULL,
  `hero_title` text DEFAULT NULL,
  `contact_email` varchar(255) DEFAULT NULL,
  `contact_phone` varchar(255) DEFAULT NULL,
  `contact_address` text DEFAULT NULL,
  `facebook_url` varchar(255) DEFAULT NULL,
  `twitter_url` varchar(255) DEFAULT NULL,
  `instagram_url` varchar(255) DEFAULT NULL,
  `line_url` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================================================
-- บันทึก Migration Records
-- ===================================================================

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '2014_10_12_000000_create_users_table', 1),
(2, '2014_10_12_100000_create_password_resets_table', 1),
(3, '2019_08_19_000000_create_failed_jobs_table', 1),
(4, '2019_12_14_000001_create_personal_access_tokens_table', 1),
(5, '2025_07_12_162611_create_services_table', 1),
(6, '2025_07_12_163000_create_packages_table', 1),
(7, '2025_07_12_163442_create_articles_table', 1),
(8, '2025_07_12_163639_create_homepage_contents_table', 1),
(9, '2025_07_12_164012_create_contacts_table', 1),
(10, '2025_07_12_190743_add_is_admin_to_users_table', 2),
(11, '2025_07_12_200139_add_is_read_to_contacts_table', 2),
(12, '2025_07_13_120000_create_site_settings_table', 3);

-- ===================================================================
-- ข้อมูลตัวอย่าง - ผู้ใช้ (Users)
-- ===================================================================

INSERT INTO `users` (`id`, `name`, `email`, `is_admin`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'Admin User', '<EMAIL>', 1, NOW(), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NULL, NOW(), NOW()),
(2, 'John Doe', '<EMAIL>', 0, NOW(), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NULL, NOW(), NOW()),
(3, 'Jane Smith', '<EMAIL>', 0, NOW(), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NULL, NOW(), NOW());

-- ===================================================================
-- ข้อมูลตัวอย่าง - บริการ (Services)
-- ===================================================================

INSERT INTO `services` (`id`, `title`, `description`, `image`, `price`, `created_at`, `updated_at`) VALUES
(1, 'การพัฒนาเว็บไซต์', 'บริการพัฒนาเว็บไซต์ที่ทันสมัยและใช้งานง่าย รองรับทุกอุปกรณ์', 'services/web-development.jpg', 15000.00, NOW(), NOW()),
(2, 'การออกแบบ UI/UX', 'บริการออกแบบส่วนติดต่อผู้ใช้ที่สวยงามและใช้งานง่าย', 'services/ui-ux-design.jpg', 8000.00, NOW(), NOW()),
(3, 'การตลาดดิจิทัล', 'บริการการตลาดออนไลน์ครบวงจร เพิ่มยอดขายและลูกค้า', 'services/digital-marketing.jpg', 12000.00, NOW(), NOW()),
(4, 'การพัฒนาแอปพลิเคชัน', 'บริการพัฒนาแอปพลิเคชันมือถือ iOS และ Android', 'services/app-development.jpg', 25000.00, NOW(), NOW());

-- ===================================================================
-- ข้อมูลตัวอย่าง - แพ็กเกจ (Packages)
-- ===================================================================

INSERT INTO `packages` (`id`, `name`, `description`, `price`, `image`, `created_at`, `updated_at`) VALUES
(1, 'แพ็กเกจเริ่มต้น', 'เหมาะสำหรับธุรกิจขนาดเล็ก ประกอบด้วยเว็บไซต์พื้นฐานและการตลาดเบื้องต้น', 20000.00, 'packages/starter-package.jpg', NOW(), NOW()),
(2, 'แพ็กเกจมาตรฐาน', 'เหมาะสำหรับธุรกิจขนาดกลาง ประกอบด้วยเว็บไซต์ครบครันและการตลาดดิจิทัล', 35000.00, 'packages/standard-package.jpg', NOW(), NOW()),
(3, 'แพ็กเกจพรีเมียม', 'เหมาะสำหรับธุรกิจขนาดใหญ่ ประกอบด้วยเว็บไซต์และแอปพลิเคชันครบครัน', 60000.00, 'packages/premium-package.jpg', NOW(), NOW());

-- ===================================================================
-- ข้อมูลตัวอย่าง - บทความ (Articles)
-- ===================================================================

INSERT INTO `articles` (`id`, `title`, `content`, `image`, `published_at`, `created_at`, `updated_at`) VALUES
(1, 'เทรนด์การพัฒนาเว็บไซต์ในปี 2025', 'การพัฒนาเว็บไซต์ในปี 2025 มีการเปลี่ยนแปลงอย่างมาก ด้วยเทคโนโลยีใหม่ๆ ที่เข้ามา เช่น AI, Machine Learning และ Progressive Web Apps ที่ทำให้เว็บไซต์มีประสิทธิภาพและใช้งานง่ายมากขึ้น', 'articles/web-trends-2025.jpg', NOW(), NOW(), NOW()),
(2, 'วิธีเลือกบริษัทพัฒนาเว็บไซต์ที่ดี', 'การเลือกบริษัทพัฒนาเว็บไซต์ที่เหมาะสมเป็นสิ่งสำคัญ ควรพิจารณาจากผลงาน ประสบการณ์ ราคา และการบริการหลังการขาย', 'articles/choose-web-company.jpg', NOW(), NOW(), NOW()),
(3, 'ความสำคัญของ SEO สำหรับธุรกิจออนไลน์', 'SEO หรือการปรับปรุงเว็บไซต์ให้ติดอันดับการค้นหาเป็นสิ่งจำเป็นสำหรับธุรกิจออนไลน์ในยุคปัจจุบัน', 'articles/seo-importance.jpg', NOW(), NOW(), NOW());

-- ===================================================================
-- ข้อมูลตัวอย่าง - เนื้อหาหน้าแรก (Homepage Contents)
-- ===================================================================

INSERT INTO `homepage_contents` (`id`, `section`, `title`, `content`, `image`, `button_text`, `button_link`, `created_at`, `updated_at`) VALUES
(1, 'hero', 'ยินดีต้อนรับสู่ SoloShop', 'เราเป็นผู้เชี่ยวชาญด้านการพัฒนาเว็บไซต์และการตลาดดิจิทัล พร้อมให้บริการที่ดีที่สุดสำหรับธุรกิจของคุณ', 'homepage/hero-banner.jpg', 'เริ่มต้นเลย', '/services', NOW(), NOW()),
(2, 'about', 'เกี่ยวกับเรา', 'SoloShop เป็นบริษัทที่มีประสบการณ์มากกว่า 5 ปีในการพัฒนาเว็บไซต์และการตลาดดิจิทัล เรามีทีมงานมืออาชีพที่พร้อมช่วยให้ธุรกิจของคุณเติบโตในโลกออนไลน์', 'homepage/about-us.jpg', 'อ่านเพิ่มเติม', '/about', NOW(), NOW()),
(3, 'services', 'บริการของเรา', 'เรามีบริการครบครันตั้งแต่การพัฒนาเว็บไซต์ การออกแบบ UI/UX การตลาดดิจิทัล และการพัฒนาแอปพลิเคชัน', 'homepage/our-services.jpg', 'ดูบริการทั้งหมด', '/services', NOW(), NOW()),
(4, 'contact', 'ติดต่อเรา', 'พร้อมให้คำปรึกษาและรับฟังความต้องการของคุณ ติดต่อเราได้ตลอด 24 ชั่วโมง', 'homepage/contact-us.jpg', 'ติดต่อเรา', '/contact', NOW(), NOW());

-- ===================================================================
-- ข้อมูลตัวอย่าง - การติดต่อ (Contacts)
-- ===================================================================

INSERT INTO `contacts` (`id`, `name`, `phone`, `email`, `address`, `message`, `is_read`, `created_at`, `updated_at`) VALUES
(1, 'สมชาย ใจดี', '************', '<EMAIL>', '123 ถนนสุขุมวิท กรุงเทพฯ 10110', 'สนใจบริการพัฒนาเว็บไซต์สำหรับร้านอาหาร', 0, NOW(), NOW()),
(2, 'สมหญิง รักงาน', '************', '<EMAIL>', '456 ถนนพหลโยธิน เชียงใหม่ 50000', 'ต้องการปรึกษาเรื่องการตลาดออนไลน์', 1, NOW(), NOW()),
(3, 'วิชัย ประสบการณ์', '************', '<EMAIL>', '789 ถนนราชดำเนิน ภูเก็ต 83000', 'สนใจแพ็กเกจพรีเมียมสำหรับธุรกิจโรงแรม', 0, NOW(), NOW());

-- ===================================================================
-- ข้อมูลตัวอย่าง - การตั้งค่าเว็บไซต์ (Site Settings)
-- ===================================================================

INSERT INTO `site_settings` (`id`, `site_name`, `site_description`, `site_logo`, `site_favicon`, `contact_email`, `contact_phone`, `contact_address`, `facebook_url`, `twitter_url`, `instagram_url`, `line_url`, `created_at`, `updated_at`) VALUES
(1, 'SoloShop', 'ผู้เชี่ยวชาญด้านการพัฒนาเว็บไซต์และการตลาดดิจิทัล', 'settings/logo.png', 'settings/favicon.ico', '<EMAIL>', '02-123-4567', '123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110', 'https://facebook.com/soloshop', 'https://twitter.com/soloshop', 'https://instagram.com/soloshop', 'https://line.me/ti/p/@soloshop', NOW(), NOW());

-- ===================================================================
-- คำแนะนำการใช้งาน
-- ===================================================================

/*
คำแนะนำการนำเข้าฐานข้อมูลใน XAMPP:

1. เปิด XAMPP Control Panel
2. เริ่มต้น Apache และ MySQL
3. เปิด phpMyAdmin (http://localhost/phpmyadmin)
4. คลิก "Import" ในเมนูด้านบน
5. เลือกไฟล์ soloshop_database.sql นี้
6. คลิก "Go" เพื่อนำเข้าฐานข้อมูล

ข้อมูลการเข้าสู่ระบบ:
- Admin: <EMAIL> / password
- User: <EMAIL> / password
- User: <EMAIL> / password

หมายเหตุ: รหัสผ่านที่เข้ารหัสแล้วคือ "password"
*/
