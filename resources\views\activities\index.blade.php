@extends('layouts.app')

@section('title', 'กิจกรรม')

@section('content')
<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">กิจกรรมของเรา</h1>
                <p class="lead mb-4">ชมภาพบรรยากาศและกิจกรรมต่างๆ ที่เราได้จัดขึ้น เพื่อให้บริการที่ดีที่สุดแก่ครอบครัวที่สูญเสีย</p>
            </div>
            <div class="col-lg-4 text-center">
                <i class="fas fa-images fa-5x opacity-75"></i>
            </div>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex flex-wrap gap-2">
                    <a href="{{ route('activities.index') }}" 
                       class="btn {{ !request('category') ? 'btn-primary' : 'btn-outline-primary' }}">
                        <i class="fas fa-th me-1"></i>ทั้งหมด
                    </a>
                    @foreach($categories as $category)
                        <a href="{{ route('activities.index', ['category' => $category->id]) }}" 
                           class="btn {{ request('category') == $category->id ? 'btn-primary' : 'btn-outline-primary' }}"
                           style="border-color: {{ $category->color }}; {{ request('category') == $category->id ? 'background-color: ' . $category->color . '; border-color: ' . $category->color : 'color: ' . $category->color }}">
                            {{ $category->name }}
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Activities Section -->
<section class="py-5">
    <div class="container">
        @if($activities->count() > 0)
            <div class="row g-4">
                @foreach($activities as $activity)
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 shadow-sm">
                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($activity->cover_image) }}"
                             class="card-img-top"
                             alt="{{ $activity->title }}"
                             style="height: 250px; object-fit: cover;">
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <span class="badge" style="background-color: {{ $activity->category->color }}">
                                    {{ $activity->category->name }}
                                </span>
                                @if($activity->activity_date)
                                    <small class="text-muted ms-2">
                                        <i class="fas fa-calendar me-1"></i>{{ $activity->activity_date->format('d/m/Y') }}
                                    </small>
                                @endif
                            </div>
                            <h5 class="card-title fw-bold text-primary">{{ Str::limit($activity->title, 60) }}</h5>
                            <p class="card-text text-muted flex-grow-1">{{ Str::limit(strip_tags($activity->description), 120) }}</p>
                            @if($activity->location)
                                <p class="card-text">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ $activity->location }}
                                    </small>
                                </p>
                            @endif
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-images me-1"></i>{{ $activity->images->count() }} รูปภาพ
                                    </small>
                                    <a href="{{ route('activities.show', $activity) }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>ดูกิจกรรม
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-images fa-5x text-muted mb-4"></i>
                <h3 class="text-muted">ยังไม่มีกิจกรรม</h3>
                <p class="text-muted">เรากำลังเตรียมกิจกรรมที่น่าสนใจสำหรับคุณ</p>
            </div>
        @endif
    </div>
</section>
@endsection
