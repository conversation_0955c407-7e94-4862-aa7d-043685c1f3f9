@extends('layouts.app')

@section('title', $activity->title)

@section('content')
<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb" class="mb-3">
                    <ol class="breadcrumb text-white-50">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white-50">หน้าแรก</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('activities.index') }}" class="text-white-50">กิจกรรม</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">{{ Str::limit($activity->title, 50) }}</li>
                    </ol>
                </nav>
                <h1 class="display-5 fw-bold mb-3">{{ $activity->title }}</h1>
                <div class="d-flex flex-wrap gap-3 mb-3">
                    <span class="badge fs-6" style="background-color: {{ $activity->category->color }}">
                        {{ $activity->category->name }}
                    </span>
                    @if($activity->activity_date)
                        <span class="text-white-75">
                            <i class="fas fa-calendar me-1"></i>{{ $activity->activity_date->format('d/m/Y') }}
                        </span>
                    @endif
                    @if($activity->location)
                        <span class="text-white-75">
                            <i class="fas fa-map-marker-alt me-1"></i>{{ $activity->location }}
                        </span>
                    @endif
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <img src="{{ \App\Helpers\ImageHelper::getImageUrl($activity->cover_image) }}"
                     class="img-fluid rounded shadow"
                     alt="{{ $activity->title }}"
                     style="max-height: 300px; object-fit: cover;">
            </div>
        </div>
    </div>
</section>

<!-- Activity Content Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- Description -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <h3 class="card-title fw-bold text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i>รายละเอียดกิจกรรม
                        </h3>
                        <div class="activity-description">
                            {!! nl2br(e($activity->description)) !!}
                        </div>
                    </div>
                </div>

                <!-- Gallery -->
                @if($activity->images->count() > 0)
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h3 class="card-title fw-bold text-primary mb-4">
                            <i class="fas fa-images me-2"></i>แกลเลอรี่ภาพ ({{ $activity->images->count() }} รูป)
                        </h3>
                        <div class="row g-3">
                            @foreach($activity->images as $image)
                            <div class="col-lg-4 col-md-6">
                                <div class="gallery-item">
                                    <img src="{{ \App\Helpers\ImageHelper::getImageUrl($image->image_path) }}"
                                         class="img-fluid rounded shadow-sm"
                                         alt="{{ $image->caption ?? $activity->title }}"
                                         style="height: 200px; width: 100%; object-fit: cover; cursor: pointer;"
                                         data-bs-toggle="modal"
                                         data-bs-target="#imageModal"
                                         data-image="{{ \App\Helpers\ImageHelper::getImageUrl($image->image_path) }}"
                                         data-caption="{{ $image->caption ?? $activity->title }}">
                                    @if($image->caption)
                                        <p class="text-muted mt-2 mb-0 small">{{ $image->caption }}</p>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Share Section -->
                <div class="card shadow-sm sticky-top mb-4" style="top: 100px;">
                    <div class="card-body">
                        <h5 class="card-title fw-bold text-primary mb-3">
                            <i class="fas fa-share-alt me-2"></i>แชร์กิจกรรม
                        </h5>
                        <div class="d-grid gap-2 mb-4">
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->fullUrl()) }}" 
                               target="_blank" class="btn btn-primary">
                                <i class="fab fa-facebook-f me-2"></i>แชร์ใน Facebook
                            </a>
                            <a href="https://line.me/R/msg/text/?{{ urlencode($activity->title . ' - ' . request()->fullUrl()) }}" 
                               target="_blank" class="btn btn-success">
                                <i class="fab fa-line me-2"></i>แชร์ใน LINE
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Related Activities -->
                @if($relatedActivities->count() > 0)
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title fw-bold text-primary mb-3">
                            <i class="fas fa-images me-2"></i>กิจกรรมที่เกี่ยวข้อง
                        </h5>
                        @foreach($relatedActivities as $related)
                        <div class="d-flex mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                            <img src="{{ \App\Helpers\ImageHelper::getImageUrl($related->cover_image) }}"
                                 class="rounded me-3"
                                 alt="{{ $related->title }}"
                                 style="width: 80px; height: 60px; object-fit: cover;">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <a href="{{ route('activities.show', $related) }}" 
                                       class="text-decoration-none text-dark">
                                        {{ Str::limit($related->title, 50) }}
                                    </a>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-images me-1"></i>{{ $related->images->count() }} รูป
                                </small>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">ภาพกิจกรรม</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="">
                <p id="modalCaption" class="mt-3 text-muted"></p>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalCaption = document.getElementById('modalCaption');
    
    imageModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const imageSrc = button.getAttribute('data-image');
        const caption = button.getAttribute('data-caption');
        
        modalImage.src = imageSrc;
        modalCaption.textContent = caption;
    });
});
</script>
@endpush
@endsection
