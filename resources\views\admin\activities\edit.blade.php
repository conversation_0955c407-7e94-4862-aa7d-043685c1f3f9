@extends('layouts.app')

@section('title', 'แก้ไขกิจกรรม')

@section('content')
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">แก้ไขกิจกรรม</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">หน้าแรก</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.activities.index') }}">จัดการกิจกรรม</a></li>
                        <li class="breadcrumb-item active">แก้ไขกิจกรรม</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">แก้ไขข้อมูลกิจกรรม</h3>
                        </div>
                        <form action="{{ route('admin.activities.update', $activity) }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')
                            <div class="card-body">
                                @if($errors->any())
                                    <div class="alert alert-danger">
                                        <ul class="mb-0">
                                            @foreach($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label for="title">ชื่อกิจกรรม <span class="text-danger">*</span></label>
                                            <input type="text"
                                                   name="title"
                                                   id="title"
                                                   class="form-control @error('title') is-invalid @enderror"
                                                   value="{{ old('title', $activity->title) }}"
                                                   placeholder="เช่น งานบุญประจำปี 2567"
                                                   required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="description">รายละเอียดกิจกรรม <span class="text-danger">*</span></label>
                                            <textarea name="description"
                                                      id="description"
                                                      class="form-control @error('description') is-invalid @enderror"
                                                      rows="6"
                                                      placeholder="อธิบายรายละเอียดของกิจกรรม..."
                                                      required>{{ old('description', $activity->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="activity_date">วันที่จัดกิจกรรม</label>
                                                    <input type="date"
                                                           name="activity_date"
                                                           id="activity_date"
                                                           class="form-control @error('activity_date') is-invalid @enderror"
                                                           value="{{ old('activity_date', $activity->activity_date ? $activity->activity_date->format('Y-m-d') : '') }}">
                                                    @error('activity_date')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="location">สถานที่</label>
                                                    <input type="text"
                                                           name="location"
                                                           id="location"
                                                           class="form-control @error('location') is-invalid @enderror"
                                                           value="{{ old('location', $activity->location) }}"
                                                           placeholder="เช่น วัดพระแก้ว">
                                                    @error('location')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="category_id">หมวดหมู่ <span class="text-danger">*</span></label>
                                            <select name="category_id"
                                                    id="category_id"
                                                    class="form-control @error('category_id') is-invalid @enderror"
                                                    required>
                                                <option value="">เลือกหมวดหมู่</option>
                                                @foreach($categories as $category)
                                                    <option value="{{ $category->id }}" 
                                                            {{ old('category_id', $activity->category_id) == $category->id ? 'selected' : '' }}>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('category_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                <a href="{{ route('admin.activity-categories.index') }}" target="_blank">
                                                    จัดการหมวดหมู่
                                                </a>
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" 
                                                       class="custom-control-input" 
                                                       id="is_published" 
                                                       name="is_published"
                                                       {{ old('is_published', $activity->is_published) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="is_published">เผยแพร่กิจกรรม</label>
                                            </div>
                                            <small class="form-text text-muted">
                                                หากไม่เลือก กิจกรรมจะถูกบันทึกเป็นร่าง
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <!-- Current Cover Image -->
                                @if($activity->cover_image)
                                <div class="form-group">
                                    <label>รูปภาพหน้าปกปัจจุบัน</label>
                                    <div class="mb-2">
                                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($activity->cover_image) }}"
                                             class="img-thumbnail"
                                             style="max-width: 200px;"
                                             alt="รูปภาพหน้าปกปัจจุบัน">
                                    </div>
                                </div>
                                @endif

                                <!-- Cover Image Section -->
                                <div class="form-group">
                                    <label for="cover_image">รูปภาพหน้าปก{{ $activity->cover_image ? ' (เปลี่ยนใหม่)' : '' }}</label>
                                    <div class="custom-file">
                                        <input type="file"
                                               name="cover_image"
                                               id="cover_image"
                                               class="custom-file-input @error('cover_image') is-invalid @enderror"
                                               accept="image/*">
                                        <label class="custom-file-label" for="cover_image">เลือกรูปภาพหน้าปก...</label>
                                        @error('cover_image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB)
                                    </small>
                                    <div id="cover_preview" class="mt-2" style="display: none;">
                                        <img id="cover_preview_img" src="" class="img-thumbnail" style="max-width: 200px;">
                                    </div>
                                </div>

                                <hr>

                                <!-- Current Gallery Images -->
                                @if($activity->images->count() > 0)
                                <div class="form-group">
                                    <label>แกลเลอรี่รูปภาพปัจจุบัน</label>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        คุณสามารถลากเพื่อจัดเรียงลำดับรูปภาพ, แก้ไขคำบรรยาย, เปลี่ยนรูปภาพ หรือลบรูปภาพได้
                                    </div>
                                    <div id="sortable-gallery" class="row">
                                        @foreach($activity->images as $image)
                                        <div class="col-md-4 mb-3 gallery-item-container" data-image-id="{{ $image->id }}">
                                            <div class="card gallery-card">
                                                <div class="card-header p-2 bg-light">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <small class="text-muted">
                                                            <i class="fas fa-grip-vertical drag-handle" style="cursor: move;"></i>
                                                            ลำดับที่ {{ $image->sort_order + 1 }}
                                                        </small>
                                                        <div class="btn-group btn-group-sm">
                                                            <button type="button"
                                                                    class="btn btn-outline-primary edit-caption-btn"
                                                                    data-image-id="{{ $image->id }}"
                                                                    data-current-caption="{{ $image->caption }}"
                                                                    title="แก้ไขคำบรรยาย">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-outline-warning replace-image-btn"
                                                                    data-image-id="{{ $image->id }}"
                                                                    title="เปลี่ยนรูปภาพ">
                                                                <i class="fas fa-exchange-alt"></i>
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-outline-danger delete-image"
                                                                    data-image-id="{{ $image->id }}"
                                                                    title="ลบรูปภาพ">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <img src="{{ \App\Helpers\ImageHelper::getImageUrl($image->image_path) }}"
                                                     class="card-img-top gallery-image"
                                                     style="height: 200px; object-fit: cover;"
                                                     alt="{{ $image->caption }}"
                                                     data-image-id="{{ $image->id }}">
                                                <div class="card-body p-2">
                                                    <div class="caption-display" data-image-id="{{ $image->id }}">
                                                        @if($image->caption)
                                                            <p class="card-text small mb-0">{{ $image->caption }}</p>
                                                        @else
                                                            <p class="card-text small mb-0 text-muted">ไม่มีคำบรรยาย</p>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                                <hr>
                                @endif

                                <!-- Gallery Images Section -->
                                <div class="form-group">
                                    <label>เพิ่มรูปภาพใหม่ในแกลเลอรี่</label>
                                    <div id="gallery_container">
                                        <div class="gallery-item mb-3">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="custom-file">
                                                        <input type="file"
                                                               name="gallery_images[]"
                                                               class="custom-file-input gallery-image"
                                                               accept="image/*">
                                                        <label class="custom-file-label">เลือกรูปภาพ...</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <input type="text"
                                                           name="captions[]"
                                                           class="form-control"
                                                           placeholder="คำบรรยายรูปภาพ (ไม่บังคับ)">
                                                </div>
                                            </div>
                                            <div class="image-preview mt-2" style="display: none;">
                                                <img src="" class="img-thumbnail" style="max-width: 150px;">
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" id="add_gallery_image" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-plus"></i> เพิ่มรูปภาพ
                                    </button>
                                    <small class="form-text text-muted d-block mt-2">
                                        รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB ต่อรูป)
                                    </small>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>บันทึกการแก้ไข
                                </button>
                                <a href="{{ route('admin.activities.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                                </a>
                                <a href="{{ route('activities.show', $activity) }}" class="btn btn-info" target="_blank">
                                    <i class="fas fa-eye me-1"></i>ดูกิจกรรม
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal สำหรับแก้ไขคำบรรยาย -->
<div class="modal fade" id="editCaptionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">แก้ไขคำบรรยายรูปภาพ</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editCaptionForm">
                    <div class="form-group">
                        <label for="editCaption">คำบรรยาย</label>
                        <textarea id="editCaption" class="form-control" rows="3" placeholder="ใส่คำบรรยายรูปภาพ..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">ยกเลิก</button>
                <button type="button" class="btn btn-primary" id="saveCaptionBtn">บันทึก</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal สำหรับเปลี่ยนรูปภาพ -->
<div class="modal fade" id="replaceImageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">เปลี่ยนรูปภาพ</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="replaceImageForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="newImage">เลือกรูปภาพใหม่</label>
                        <div class="custom-file">
                            <input type="file" id="newImage" class="custom-file-input" accept="image/*" required>
                            <label class="custom-file-label" for="newImage">เลือกรูปภาพ...</label>
                        </div>
                        <small class="form-text text-muted">รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB)</small>
                    </div>
                    <div class="form-group">
                        <label for="replaceCaption">คำบรรยาย</label>
                        <textarea id="replaceCaption" class="form-control" rows="3" placeholder="ใส่คำบรรยายรูปภาพ..."></textarea>
                    </div>
                    <div id="replacePreview" class="form-group" style="display: none;">
                        <label>ตัวอย่างรูปภาพใหม่</label>
                        <div>
                            <img id="replacePreviewImg" src="" class="img-thumbnail" style="max-width: 200px;">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">ยกเลิก</button>
                <button type="button" class="btn btn-warning" id="replaceImageBtn">เปลี่ยนรูปภาพ</button>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.gallery-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.gallery-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

.drag-handle {
    cursor: move;
}

.sortable-ghost {
    opacity: 0.5;
}

.sortable-chosen {
    border-color: #28a745 !important;
}

#sortable-gallery .gallery-item-container {
    cursor: move;
}

.gallery-image {
    transition: transform 0.2s ease;
}

.gallery-image:hover {
    transform: scale(1.02);
}
</style>
@endpush

@push('scripts')
<!-- Include SortableJS -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentImageId = null;
    // Handle cover image preview
    document.getElementById('cover_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('cover_preview');
        const previewImg = document.getElementById('cover_preview_img');
        
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
        }
    });

    // Handle gallery images
    let galleryIndex = 1;
    
    document.getElementById('add_gallery_image').addEventListener('click', function() {
        const container = document.getElementById('gallery_container');
        const newItem = document.createElement('div');
        newItem.className = 'gallery-item mb-3';
        newItem.innerHTML = `
            <div class="row">
                <div class="col-md-8">
                    <div class="custom-file">
                        <input type="file"
                               name="gallery_images[]"
                               class="custom-file-input gallery-image"
                               accept="image/*">
                        <label class="custom-file-label">เลือกรูปภาพ...</label>
                    </div>
                </div>
                <div class="col-md-3">
                    <input type="text"
                           name="captions[]"
                           class="form-control"
                           placeholder="คำบรรยายรูปภาพ (ไม่บังคับ)">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger btn-sm remove-gallery-item">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="image-preview mt-2" style="display: none;">
                <img src="" class="img-thumbnail" style="max-width: 150px;">
            </div>
        `;
        container.appendChild(newItem);
        galleryIndex++;
    });

    // Handle gallery image preview and removal
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('gallery-image')) {
            const file = e.target.files[0];
            const preview = e.target.closest('.gallery-item').querySelector('.image-preview');
            const previewImg = preview.querySelector('img');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
            }
        }
    });

    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-gallery-item') || e.target.closest('.remove-gallery-item')) {
            e.target.closest('.gallery-item').remove();
        }
    });

    // Update file input labels
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('custom-file-input')) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'เลือกรูปภาพ...';
            const label = e.target.nextElementSibling;
            label.textContent = fileName;
        }
    });

    // Initialize Sortable for gallery
    const sortableGallery = document.getElementById('sortable-gallery');
    if (sortableGallery) {
        new Sortable(sortableGallery, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            handle: '.drag-handle',
            onEnd: function(evt) {
                const imageIds = Array.from(sortableGallery.children).map(item =>
                    item.getAttribute('data-image-id')
                );

                // Update sort order on server
                fetch(`{{ route('admin.activities.images.update-order', $activity->id) }}`, {
                    method: 'PUT',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image_ids: imageIds
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update order numbers in UI
                        sortableGallery.querySelectorAll('.gallery-item-container').forEach((item, index) => {
                            const orderText = item.querySelector('.drag-handle').parentNode;
                            orderText.innerHTML = `<i class="fas fa-grip-vertical drag-handle" style="cursor: move;"></i> ลำดับที่ ${index + 1}`;
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
        });
    }

    // Handle edit caption
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('edit-caption-btn') || e.target.closest('.edit-caption-btn')) {
            const button = e.target.classList.contains('edit-caption-btn') ? e.target : e.target.closest('.edit-caption-btn');
            currentImageId = button.getAttribute('data-image-id');
            const currentCaption = button.getAttribute('data-current-caption') || '';

            document.getElementById('editCaption').value = currentCaption;
            $('#editCaptionModal').modal('show');
        }
    });

    // Save caption
    document.getElementById('saveCaptionBtn').addEventListener('click', function() {
        const newCaption = document.getElementById('editCaption').value;

        fetch(`{{ route('admin.activities.images.update-caption', '') }}/${currentImageId}`, {
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                caption: newCaption
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update caption in UI
                const captionDisplay = document.querySelector(`.caption-display[data-image-id="${currentImageId}"] p`);
                if (newCaption.trim()) {
                    captionDisplay.textContent = newCaption;
                    captionDisplay.classList.remove('text-muted');
                } else {
                    captionDisplay.textContent = 'ไม่มีคำบรรยาย';
                    captionDisplay.classList.add('text-muted');
                }

                // Update button data
                const editBtn = document.querySelector(`.edit-caption-btn[data-image-id="${currentImageId}"]`);
                editBtn.setAttribute('data-current-caption', newCaption);

                $('#editCaptionModal').modal('hide');
            } else {
                alert('เกิดข้อผิดพลาดในการบันทึกคำบรรยาย');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการบันทึกคำบรรยาย');
        });
    });

    // Handle replace image
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('replace-image-btn') || e.target.closest('.replace-image-btn')) {
            const button = e.target.classList.contains('replace-image-btn') ? e.target : e.target.closest('.replace-image-btn');
            currentImageId = button.getAttribute('data-image-id');

            // Get current caption
            const currentCaption = document.querySelector(`.edit-caption-btn[data-image-id="${currentImageId}"]`).getAttribute('data-current-caption') || '';
            document.getElementById('replaceCaption').value = currentCaption;

            $('#replaceImageModal').modal('show');
        }
    });

    // Handle new image preview for replace
    document.getElementById('newImage').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('replacePreview');
        const previewImg = document.getElementById('replacePreviewImg');

        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);

            // Update label
            const label = document.querySelector('label[for="newImage"]');
            label.textContent = file.name;
        } else {
            preview.style.display = 'none';
        }
    });

    // Replace image
    document.getElementById('replaceImageBtn').addEventListener('click', function() {
        const fileInput = document.getElementById('newImage');
        const caption = document.getElementById('replaceCaption').value;

        if (!fileInput.files[0]) {
            alert('กรุณาเลือกรูปภาพใหม่');
            return;
        }

        const formData = new FormData();
        formData.append('new_image', fileInput.files[0]);
        formData.append('caption', caption);

        fetch(`{{ route('admin.activities.images.replace', '') }}/${currentImageId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update image in UI
                const galleryImage = document.querySelector(`.gallery-image[data-image-id="${currentImageId}"]`);
                galleryImage.src = data.new_image_url;

                // Update caption
                const captionDisplay = document.querySelector(`.caption-display[data-image-id="${currentImageId}"] p`);
                if (caption.trim()) {
                    captionDisplay.textContent = caption;
                    captionDisplay.classList.remove('text-muted');
                } else {
                    captionDisplay.textContent = 'ไม่มีคำบรรยาย';
                    captionDisplay.classList.add('text-muted');
                }

                // Update button data
                const editBtn = document.querySelector(`.edit-caption-btn[data-image-id="${currentImageId}"]`);
                editBtn.setAttribute('data-current-caption', caption);

                $('#replaceImageModal').modal('hide');

                // Reset form
                document.getElementById('replaceImageForm').reset();
                document.getElementById('replacePreview').style.display = 'none';
                document.querySelector('label[for="newImage"]').textContent = 'เลือกรูปภาพ...';
            } else {
                alert(data.message || 'เกิดข้อผิดพลาดในการเปลี่ยนรูปภาพ');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการเปลี่ยนรูปภาพ');
        });
    });

    // Handle delete existing images
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('delete-image') || e.target.closest('.delete-image')) {
            const button = e.target.classList.contains('delete-image') ? e.target : e.target.closest('.delete-image');
            const imageId = button.getAttribute('data-image-id');

            if (confirm('คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?')) {
                fetch(`{{ route('admin.activities.images.delete', ['activity' => $activity->id, 'image' => '']) }}/${imageId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        button.closest('.gallery-item-container').remove();
                    } else {
                        alert('เกิดข้อผิดพลาดในการลบรูปภาพ');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการลบรูปภาพ');
                });
            }
        }
    });
});
</script>
@endpush
@endsection
