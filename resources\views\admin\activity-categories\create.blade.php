@extends('layouts.app')

@section('title', 'เพิ่มหมวดหมู่ใหม่')

@section('content')
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">เพิ่มหมวดหมู่ใหม่</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">หน้าแรก</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.activities.index') }}">จัดการกิจกรรม</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.activity-categories.index') }}">จัดการหมวดหมู่</a></li>
                        <li class="breadcrumb-item active">เพิ่มหมวดหมู่ใหม่</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">ข้อมูลหมวดหมู่</h3>
                        </div>
                        <form action="{{ route('admin.activity-categories.store') }}" method="POST">
                            @csrf
                            <div class="card-body">
                                @if($errors->any())
                                    <div class="alert alert-danger">
                                        <ul class="mb-0">
                                            @foreach($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <div class="form-group">
                                    <label for="name">ชื่อหมวดหมู่ <span class="text-danger">*</span></label>
                                    <input type="text"
                                           name="name"
                                           id="name"
                                           class="form-control @error('name') is-invalid @enderror"
                                           value="{{ old('name') }}"
                                           placeholder="เช่น งานบุญ, งานศพ, กิจกรรมชุมชน"
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="description">คำอธิบาย</label>
                                    <textarea name="description"
                                              id="description"
                                              class="form-control @error('description') is-invalid @enderror"
                                              rows="3"
                                              placeholder="อธิบายเกี่ยวกับหมวดหมู่นี้...">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="color">สีของหมวดหมู่ <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="color"
                                               name="color"
                                               id="color"
                                               class="form-control @error('color') is-invalid @enderror"
                                               value="{{ old('color', '#007bff') }}"
                                               style="width: 60px; height: 38px;"
                                               required>
                                        <input type="text"
                                               id="color_text"
                                               class="form-control"
                                               value="{{ old('color', '#007bff') }}"
                                               readonly>
                                    </div>
                                    @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        เลือกสีที่จะใช้แสดงหมวดหมู่นี้
                                    </small>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" 
                                               class="custom-control-input" 
                                               id="is_active" 
                                               name="is_active"
                                               {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">เปิดใช้งานหมวดหมู่</label>
                                    </div>
                                    <small class="form-text text-muted">
                                        หากไม่เลือก หมวดหมู่จะไม่แสดงในการเลือก
                                    </small>
                                </div>

                                <!-- Preview -->
                                <div class="form-group">
                                    <label>ตัวอย่าง</label>
                                    <div class="d-flex align-items-center">
                                        <div id="color_preview" 
                                             style="width: 20px; height: 20px; background-color: {{ old('color', '#007bff') }}; border-radius: 4px; margin-right: 10px;"></div>
                                        <span id="name_preview" class="badge" style="background-color: {{ old('color', '#007bff') }};">
                                            {{ old('name', 'ชื่อหมวดหมู่') }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>บันทึกหมวดหมู่
                                </button>
                                <a href="{{ route('admin.activity-categories.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">คำแนะนำ</h3>
                        </div>
                        <div class="card-body">
                            <h5>การตั้งชื่อหมวดหมู่</h5>
                            <ul>
                                <li>ใช้ชื่อที่สั้นและเข้าใจง่าย</li>
                                <li>หลีกเลี่ยงการใช้คำที่ซ้ำกัน</li>
                                <li>ควรเป็นคำที่เกี่ยวข้องกับประเภทกิจกรรม</li>
                            </ul>

                            <h5 class="mt-4">การเลือกสี</h5>
                            <ul>
                                <li>เลือกสีที่แตกต่างกันสำหรับแต่ละหมวดหมู่</li>
                                <li>ใช้สีที่เหมาะสมกับธีมของเว็บไซต์</li>
                                <li>หลีกเลี่ยงสีที่อ่านยาก</li>
                            </ul>

                            <h5 class="mt-4">ตัวอย่างหมวดหมู่</h5>
                            <div class="d-flex flex-wrap gap-2">
                                <span class="badge" style="background-color: #28a745;">งานบุญ</span>
                                <span class="badge" style="background-color: #6c757d;">งานศพ</span>
                                <span class="badge" style="background-color: #17a2b8;">กิจกรรมชุมชน</span>
                                <span class="badge" style="background-color: #ffc107; color: #000;">งานเทศกาล</span>
                                <span class="badge" style="background-color: #dc3545;">งานพิเศษ</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const colorInput = document.getElementById('color');
    const colorText = document.getElementById('color_text');
    const colorPreview = document.getElementById('color_preview');
    const nameInput = document.getElementById('name');
    const namePreview = document.getElementById('name_preview');

    // Update color preview
    colorInput.addEventListener('input', function() {
        const color = this.value;
        colorText.value = color;
        colorPreview.style.backgroundColor = color;
        namePreview.style.backgroundColor = color;
    });

    // Update name preview
    nameInput.addEventListener('input', function() {
        const name = this.value || 'ชื่อหมวดหมู่';
        namePreview.textContent = name;
    });
});
</script>
@endpush
@endsection
