@extends('layouts.app')

@section('title', 'แก้ไขหมวดหมู่')

@section('content')
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">แก้ไขหมวดหมู่</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">หน้าแรก</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.activities.index') }}">จัดการกิจกรรม</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.activity-categories.index') }}">จัดการหมวดหมู่</a></li>
                        <li class="breadcrumb-item active">แก้ไขหมวดหมู่</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">แก้ไขข้อมูลหมวดหมู่</h3>
                        </div>
                        <form action="{{ route('admin.activity-categories.update', $activityCategory) }}" method="POST">
                            @csrf
                            @method('PUT')
                            <div class="card-body">
                                @if($errors->any())
                                    <div class="alert alert-danger">
                                        <ul class="mb-0">
                                            @foreach($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <div class="form-group">
                                    <label for="name">ชื่อหมวดหมู่ <span class="text-danger">*</span></label>
                                    <input type="text"
                                           name="name"
                                           id="name"
                                           class="form-control @error('name') is-invalid @enderror"
                                           value="{{ old('name', $activityCategory->name) }}"
                                           placeholder="เช่น งานบุญ, งานศพ, กิจกรรมชุมชน"
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="description">คำอธิบาย</label>
                                    <textarea name="description"
                                              id="description"
                                              class="form-control @error('description') is-invalid @enderror"
                                              rows="3"
                                              placeholder="อธิบายเกี่ยวกับหมวดหมู่นี้...">{{ old('description', $activityCategory->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="color">สีของหมวดหมู่ <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="color"
                                               name="color"
                                               id="color"
                                               class="form-control @error('color') is-invalid @enderror"
                                               value="{{ old('color', $activityCategory->color) }}"
                                               style="width: 60px; height: 38px;"
                                               required>
                                        <input type="text"
                                               id="color_text"
                                               class="form-control"
                                               value="{{ old('color', $activityCategory->color) }}"
                                               readonly>
                                    </div>
                                    @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        เลือกสีที่จะใช้แสดงหมวดหมู่นี้
                                    </small>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" 
                                               class="custom-control-input" 
                                               id="is_active" 
                                               name="is_active"
                                               {{ old('is_active', $activityCategory->is_active) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">เปิดใช้งานหมวดหมู่</label>
                                    </div>
                                    <small class="form-text text-muted">
                                        หากไม่เลือก หมวดหมู่จะไม่แสดงในการเลือก
                                    </small>
                                </div>

                                <!-- Preview -->
                                <div class="form-group">
                                    <label>ตัวอย่าง</label>
                                    <div class="d-flex align-items-center">
                                        <div id="color_preview" 
                                             style="width: 20px; height: 20px; background-color: {{ old('color', $activityCategory->color) }}; border-radius: 4px; margin-right: 10px;"></div>
                                        <span id="name_preview" class="badge" style="background-color: {{ old('color', $activityCategory->color) }};">
                                            {{ old('name', $activityCategory->name) }}
                                        </span>
                                    </div>
                                </div>

                                <!-- Activity Count Info -->
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    หมวดหมู่นี้มีกิจกรรม <strong>{{ $activityCategory->activities()->count() }}</strong> กิจกรรม
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>บันทึกการแก้ไข
                                </button>
                                <a href="{{ route('admin.activity-categories.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">กิจกรรมในหมวดหมู่นี้</h3>
                        </div>
                        <div class="card-body">
                            @if($activityCategory->activities()->count() > 0)
                                <div class="list-group">
                                    @foreach($activityCategory->activities()->latest()->limit(5)->get() as $activity)
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">{{ $activity->title }}</h6>
                                                <small class="text-muted">
                                                    @if($activity->activity_date)
                                                        <i class="fas fa-calendar me-1"></i>{{ $activity->activity_date->format('d/m/Y') }}
                                                    @endif
                                                    @if($activity->location)
                                                        <i class="fas fa-map-marker-alt ms-2 me-1"></i>{{ $activity->location }}
                                                    @endif
                                                </small>
                                            </div>
                                            <span class="badge {{ $activity->is_published ? 'badge-success' : 'badge-warning' }}">
                                                {{ $activity->is_published ? 'เผยแพร่' : 'ร่าง' }}
                                            </span>
                                        </div>
                                    @endforeach
                                </div>
                                @if($activityCategory->activities()->count() > 5)
                                    <div class="text-center mt-3">
                                        <small class="text-muted">และอีก {{ $activityCategory->activities()->count() - 5 }} กิจกรรม</small>
                                    </div>
                                @endif
                            @else
                                <div class="text-center py-3">
                                    <i class="fas fa-images fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">ยังไม่มีกิจกรรมในหมวดหมู่นี้</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const colorInput = document.getElementById('color');
    const colorText = document.getElementById('color_text');
    const colorPreview = document.getElementById('color_preview');
    const nameInput = document.getElementById('name');
    const namePreview = document.getElementById('name_preview');

    // Update color preview
    colorInput.addEventListener('input', function() {
        const color = this.value;
        colorText.value = color;
        colorPreview.style.backgroundColor = color;
        namePreview.style.backgroundColor = color;
    });

    // Update name preview
    nameInput.addEventListener('input', function() {
        const name = this.value || 'ชื่อหมวดหมู่';
        namePreview.textContent = name;
    });
});
</script>
@endpush
@endsection
