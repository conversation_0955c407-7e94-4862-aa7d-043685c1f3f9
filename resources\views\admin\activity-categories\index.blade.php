@extends('layouts.app')

@section('title', 'จัดการหมวดหมู่กิจกรรม')

@section('content')
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">จัดการหมวดหมู่กิจกรรม</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">หน้าแรก</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.activities.index') }}">จัดการกิจกรรม</a></li>
                        <li class="breadcrumb-item active">จัดการหมวดหมู่</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">รายการหมวดหมู่กิจกรรม</h3>
                            <div class="card-tools">
                                <a href="{{ route('admin.activity-categories.create') }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> เพิ่มหมวดหมู่ใหม่
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            @if(session('success'))
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    {{ session('success') }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            @endif

                            @if(session('error'))
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    {{ session('error') }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            @endif

                            @if($categories->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th style="width: 50px;">สี</th>
                                                <th>ชื่อหมวดหมู่</th>
                                                <th>คำอธิบาย</th>
                                                <th style="width: 100px;">จำนวนกิจกรรม</th>
                                                <th style="width: 80px;">สถานะ</th>
                                                <th style="width: 120px;">จัดการ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($categories as $category)
                                                <tr>
                                                    <td class="text-center">
                                                        <div class="color-box" 
                                                             style="width: 30px; height: 30px; background-color: {{ $category->color }}; border-radius: 4px; margin: 0 auto;"></div>
                                                    </td>
                                                    <td>
                                                        <strong>{{ $category->name }}</strong>
                                                    </td>
                                                    <td>
                                                        {{ $category->description ?? '-' }}
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge badge-info">
                                                            {{ $category->activities_count }}
                                                        </span>
                                                    </td>
                                                    <td class="text-center">
                                                        @if($category->is_active)
                                                            <span class="badge badge-success">ใช้งาน</span>
                                                        @else
                                                            <span class="badge badge-secondary">ปิดใช้งาน</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ route('admin.activity-categories.edit', $category) }}" 
                                                               class="btn btn-warning btn-sm" title="แก้ไข">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            @if($category->activities_count == 0)
                                                                <form action="{{ route('admin.activity-categories.destroy', $category) }}" 
                                                                      method="POST" class="d-inline"
                                                                      onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบหมวดหมู่นี้?')">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" class="btn btn-danger btn-sm" title="ลบ">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </form>
                                                            @else
                                                                <button class="btn btn-danger btn-sm" disabled title="ไม่สามารถลบได้เนื่องจากมีกิจกรรมในหมวดหมู่นี้">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">ยังไม่มีหมวดหมู่</h5>
                                    <p class="text-muted">เริ่มต้นสร้างหมวดหมู่แรกของคุณ</p>
                                    <a href="{{ route('admin.activity-categories.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>เพิ่มหมวดหมู่ใหม่
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection
