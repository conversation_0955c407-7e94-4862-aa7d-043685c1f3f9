@extends('layouts.app')

@section('title', 'แดชบอร์ด - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-tachometer-alt me-2"></i>แดชบอร์ด
                    </h1>
                    <p class="text-muted">ภาพรวมระบบจัดการเว็บไซต์ SoloShop</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item active">แดชบอร์ด</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row">
                <!-- Services Card -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ \App\Models\Service::count() }}</h3>
                            <p>บริการทั้งหมด</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <a href="{{ route('admin.services.index') }}" class="small-box-footer">
                            ดูรายละเอียด <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Packages Card -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ \App\Models\Package::count() }}</h3>
                            <p>แพ็กเกจทั้งหมด</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <a href="{{ route('admin.packages.index') }}" class="small-box-footer">
                            ดูรายละเอียด <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Activities Card -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ \App\Models\Activity::count() }}</h3>
                            <p>กิจกรรมทั้งหมด</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-images"></i>
                        </div>
                        <a href="{{ route('admin.activities.index') }}" class="small-box-footer">
                            ดูรายละเอียด <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Contacts Card -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ \App\Models\Contact::unread()->count() }}</h3>
                            <p>ข้อความใหม่</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <a href="{{ route('admin.contacts.index') }}" class="small-box-footer">
                            ดูรายละเอียด <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>


            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-bolt me-2"></i>การดำเนินการด่วน
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('admin.services.create') }}" class="btn btn-primary btn-block">
                                        <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('admin.packages.create') }}" class="btn btn-success btn-block">
                                        <i class="fas fa-plus me-2"></i>เพิ่มแพ็กเกจใหม่
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('admin.activities.create') }}" class="btn btn-info btn-block">
                                        <i class="fas fa-plus me-2"></i>เพิ่มกิจกรรมใหม่
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('admin.homepage.index') }}" class="btn btn-warning btn-block">
                                        <i class="fas fa-edit me-2"></i>แก้ไขหน้าแรก
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary btn-block">
                                        <i class="fas fa-cog me-2"></i>การตั้งค่าเว็บไซต์
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Recent Activities -->
            <div class="row mt-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-list me-2"></i>บริการล่าสุด
                            </h3>
                        </div>
                        <div class="card-body">
                            @php
                                $recentServices = \App\Models\Service::latest()->take(5)->get();
                            @endphp
                            @if($recentServices->count() > 0)
                                <div class="list-group list-group-flush">
                                    @foreach($recentServices as $service)
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">{{ $service->title }}</h6>
                                                <small class="text-muted">{{ Str::limit($service->description, 50) }}</small>
                                            </div>
                                            <a href="{{ route('admin.services.edit', $service) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-muted text-center">ยังไม่มีบริการ</p>
                            @endif
                </div>
            </div>
        </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-envelope me-2"></i>ข้อความติดต่อล่าสุด
                            </h3>
                        </div>
                        <div class="card-body">
                            @php
                                $recentContacts = \App\Models\Contact::latest()->take(5)->get();
                            @endphp
                            @if($recentContacts->count() > 0)
                                <div class="list-group list-group-flush">
                                    @foreach($recentContacts as $contact)
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">
                                                    {{ $contact->name }}
                                                    @if(!$contact->is_read)
                                                        <span class="badge badge-danger">ใหม่</span>
                                                    @endif
                                                </h6>
                                                <small class="text-muted">{{ $contact->email }} - {{ Str::limit($contact->message, 50) }}</small>
                                                <br><small class="text-muted">{{ $contact->created_at->diffForHumans() }}</small>
                                            </div>
                                            <a href="{{ route('admin.contacts.edit', $contact) }}" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-muted text-center">ยังไม่มีข้อความติดต่อ</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection