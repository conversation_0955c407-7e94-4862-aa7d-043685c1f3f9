@extends('layouts.auth')

@section('title', 'เข้าสู่ระบบ - SoloShop')

@section('content')
<form method="POST" action="{{ route('login') }}">
    @csrf

    @if($errors->any())
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>เกิดข้อผิดพลาด!</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <div class="mb-3">
        <label for="email" class="form-label">
            <i class="fas fa-envelope me-2"></i>อีเมล
        </label>
        <input id="email"
               type="email"
               class="form-control @error('email') is-invalid @enderror"
               name="email"
               value="{{ old('email') }}"
               required
               autocomplete="email"
               autofocus
               placeholder="กรุณาใส่อีเมลของคุณ">
        @error('email')
            <div class="invalid-feedback">
                <i class="fas fa-times-circle me-1"></i>{{ $message }}
            </div>
        @enderror
    </div>

    <div class="mb-3">
        <label for="password" class="form-label">
            <i class="fas fa-lock me-2"></i>รหัสผ่าน
        </label>
        <input id="password"
               type="password"
               class="form-control @error('password') is-invalid @enderror"
               name="password"
               required
               autocomplete="current-password"
               placeholder="กรุณาใส่รหัสผ่านของคุณ">
        @error('password')
            <div class="invalid-feedback">
                <i class="fas fa-times-circle me-1"></i>{{ $message }}
            </div>
        @enderror
    </div>

    <div class="mb-3">
        <div class="form-check">
            <input class="form-check-input"
                   type="checkbox"
                   name="remember"
                   id="remember"
                   {{ old('remember') ? 'checked' : '' }}>
            <label class="form-check-label" for="remember">
                จดจำการเข้าสู่ระบบ
            </label>
        </div>
    </div>

    <div class="d-grid gap-2 mb-3">
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบ
        </button>
    </div>

    @if (Route::has('password.request'))
        <div class="text-center">
            <a href="{{ route('password.request') }}" class="text-decoration-none">
                <i class="fas fa-question-circle me-1"></i>ลืมรหัสผ่าน?
            </a>
        </div>
    @endif
</form>

@if (!Route::has('register'))
    <div class="text-center mt-3">
        <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            หากยังไม่มีบัญชี กรุณาติดต่อผู้ดูแลระบบ
        </small>
    </div>
@endif
@endsection
