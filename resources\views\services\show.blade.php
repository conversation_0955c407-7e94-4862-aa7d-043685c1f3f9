@extends('layouts.app')

@section('title', $service->title . ' - ผู้ใหญ่จากบริการ')

@section('content')
<!-- Hero Section -->
<section class="hero-section text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/" class="text-white text-decoration-none">หน้าแรก</a></li>
                        <li class="breadcrumb-item"><a href="/services" class="text-white text-decoration-none">บริการ</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">{{ Str::limit($service->title, 30) }}</li>
                    </ol>
                </nav>
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-tools me-3"></i>{{ $service->title }}
                </h1>
                <div class="d-flex align-items-center mb-4">
                    <span class="h2 text-warning fw-bold me-3">
                        <i class="fas fa-tag me-1"></i>฿{{ number_format($service->price) }}
                    </span>
                    <span class="badge bg-success fs-6">พร้อมให้บริการ</span>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <img src="{{ \App\Helpers\ImageHelper::getImageUrl($service->image) }}"
                     alt="{{ $service->title }}"
                     class="img-fluid rounded shadow"
                     style="width: 100%; height: 350px; object-fit: cover;">
            </div>
        </div>
    </div>
</section>

<!-- Service Details Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h3 class="card-title fw-bold text-primary mb-4">
                            <i class="fas fa-info-circle me-2"></i>รายละเอียดบริการ
                        </h3>
                        <div class="service-description mb-4">
                            {!! nl2br(e($service->description)) !!}
                        </div>
                        
                        <h4 class="fw-bold text-primary mb-3">
                            <i class="fas fa-star me-2"></i>คุณสมบัติ
                        </h4>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-3">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <strong>คุณภาพสูง</strong> - รับประกันผลงาน
                                    </li>
                                    <li class="mb-3">
                                        <i class="fas fa-clock text-primary me-2"></i>
                                        <strong>รวดเร็ว</strong> - ทำงานตรงเวลา
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-3">
                                        <i class="fas fa-dollar-sign text-warning me-2"></i>
                                        <strong>ราคายุติธรรม</strong> - คุ้มค่าเงิน
                                    </li>
                                    <li class="mb-3">
                                        <i class="fas fa-headset text-info me-2"></i>
                                        <strong>บริการหลังการขาย</strong> - ดูแลตลอด
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card shadow-sm sticky-top" style="top: 100px;">
                    <div class="card-body">
                        <h5 class="card-title fw-bold text-primary mb-3">
                            <i class="fas fa-shopping-cart me-2"></i>สั่งซื้อบริการ
                        </h5>
                        <div class="text-center mb-4">
                            <div class="h2 text-success fw-bold mb-2">฿{{ number_format($service->price) }}</div>
                            <small class="text-muted">ราคารวมภาษีแล้ว</small>
                        </div>
                        <div class="d-grid gap-2">
                            <a href="/contact" class="btn btn-primary btn-lg">
                                <i class="fas fa-phone me-2"></i>ติดต่อสั่งซื้อ
                            </a>
                            <a href="tel:************" class="btn btn-outline-success">
                                <i class="fas fa-phone-alt me-2"></i>โทร: ************
                            </a>
                        </div>
                        <hr class="my-4">
                        <div class="text-center">
                            <h6 class="fw-bold mb-3">ข้อมูลติดต่อ</h6>
                            <div class="text-muted small">
                                <p class="mb-2"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                                <p class="mb-2"><i class="fas fa-clock me-2"></i>บริการ 24 ชั่วโมง</p>
                                <p class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>กรุงเทพมหานคร</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Services Section -->
<section class="bg-light py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h3 class="section-title">บริการอื่นๆ</h3>
            <p class="text-muted">ดูบริการอื่นๆ ที่น่าสนใจ</p>
        </div>
        @php
            $relatedServices = \App\Models\Service::where('id', '!=', $service->id)->take(3)->get();
        @endphp
        @if($relatedServices->count() > 0)
            <div class="row g-4">
                @foreach($relatedServices as $relatedService)
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 shadow-sm">
                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($relatedService->image) }}"
                             class="card-img-top"
                             alt="{{ $relatedService->title }}"
                             style="height: 200px; object-fit: cover;">
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title fw-bold text-primary">{{ $relatedService->title }}</h5>
                            <p class="card-text text-muted flex-grow-1">{{ Str::limit($relatedService->description, 120) }}</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="h4 text-success fw-bold mb-0">
                                    <i class="fas fa-tag me-1"></i>฿{{ number_format($relatedService->price) }}
                                </span>
                                <a href="{{ route('services.show', $relatedService) }}" class="btn btn-primary">
                                    <i class="fas fa-eye me-1"></i>ดูรายละเอียด
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        @else
            <div class="text-center">
                <a href="/services" class="btn btn-primary btn-lg">
                    <i class="fas fa-tools me-2"></i>ดูบริการทั้งหมด
                </a>
            </div>
        @endif
    </div>
</section>
@endsection 