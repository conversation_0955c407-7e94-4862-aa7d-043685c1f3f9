<?php $__env->startSection('title', 'แก้ไขกิจกรรม'); ?>

<?php $__env->startSection('content'); ?>
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">แก้ไขกิจกรรม</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">หน้าแรก</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.activities.index')); ?>">จัดการกิจกรรม</a></li>
                        <li class="breadcrumb-item active">แก้ไขกิจกรรม</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">แก้ไขข้อมูลกิจกรรม</h3>
                        </div>
                        <form action="<?php echo e(route('admin.activities.update', $activity)); ?>" method="POST" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <div class="card-body">
                                <?php if($errors->any()): ?>
                                    <div class="alert alert-danger">
                                        <ul class="mb-0">
                                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li><?php echo e($error); ?></li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                    </div>
                                <?php endif; ?>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label for="title">ชื่อกิจกรรม <span class="text-danger">*</span></label>
                                            <input type="text"
                                                   name="title"
                                                   id="title"
                                                   class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                   value="<?php echo e(old('title', $activity->title)); ?>"
                                                   placeholder="เช่น งานบุญประจำปี 2567"
                                                   required>
                                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>

                                        <div class="form-group">
                                            <label for="description">รายละเอียดกิจกรรม <span class="text-danger">*</span></label>
                                            <textarea name="description"
                                                      id="description"
                                                      class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                      rows="6"
                                                      placeholder="อธิบายรายละเอียดของกิจกรรม..."
                                                      required><?php echo e(old('description', $activity->description)); ?></textarea>
                                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="activity_date">วันที่จัดกิจกรรม</label>
                                                    <input type="date"
                                                           name="activity_date"
                                                           id="activity_date"
                                                           class="form-control <?php $__errorArgs = ['activity_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           value="<?php echo e(old('activity_date', $activity->activity_date ? $activity->activity_date->format('Y-m-d') : '')); ?>">
                                                    <?php $__errorArgs = ['activity_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="location">สถานที่</label>
                                                    <input type="text"
                                                           name="location"
                                                           id="location"
                                                           class="form-control <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                           value="<?php echo e(old('location', $activity->location)); ?>"
                                                           placeholder="เช่น วัดพระแก้ว">
                                                    <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="category_id">หมวดหมู่ <span class="text-danger">*</span></label>
                                            <select name="category_id"
                                                    id="category_id"
                                                    class="form-control <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                    required>
                                                <option value="">เลือกหมวดหมู่</option>
                                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($category->id); ?>" 
                                                            <?php echo e(old('category_id', $activity->category_id) == $category->id ? 'selected' : ''); ?>>
                                                        <?php echo e($category->name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            <small class="form-text text-muted">
                                                <a href="<?php echo e(route('admin.activity-categories.index')); ?>" target="_blank">
                                                    จัดการหมวดหมู่
                                                </a>
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" 
                                                       class="custom-control-input" 
                                                       id="is_published" 
                                                       name="is_published"
                                                       <?php echo e(old('is_published', $activity->is_published) ? 'checked' : ''); ?>>
                                                <label class="custom-control-label" for="is_published">เผยแพร่กิจกรรม</label>
                                            </div>
                                            <small class="form-text text-muted">
                                                หากไม่เลือก กิจกรรมจะถูกบันทึกเป็นร่าง
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <!-- Current Cover Image -->
                                <?php if($activity->cover_image): ?>
                                <div class="form-group">
                                    <label>รูปภาพหน้าปกปัจจุบัน</label>
                                    <div class="mb-2">
                                        <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($activity->cover_image)); ?>"
                                             class="img-thumbnail"
                                             style="max-width: 200px;"
                                             alt="รูปภาพหน้าปกปัจจุบัน">
                                    </div>
                                </div>
                                <?php endif; ?>

                                <!-- Cover Image Section -->
                                <div class="form-group">
                                    <label for="cover_image">รูปภาพหน้าปก<?php echo e($activity->cover_image ? ' (เปลี่ยนใหม่)' : ''); ?></label>
                                    <div class="custom-file">
                                        <input type="file"
                                               name="cover_image"
                                               id="cover_image"
                                               class="custom-file-input <?php $__errorArgs = ['cover_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               accept="image/*">
                                        <label class="custom-file-label" for="cover_image">เลือกรูปภาพหน้าปก...</label>
                                        <?php $__errorArgs = ['cover_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <small class="form-text text-muted">
                                        รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB)
                                    </small>
                                    <div id="cover_preview" class="mt-2" style="display: none;">
                                        <img id="cover_preview_img" src="" class="img-thumbnail" style="max-width: 200px;">
                                    </div>
                                </div>

                                <hr>

                                <!-- Current Gallery Images -->
                                <?php if($activity->images->count() > 0): ?>
                                <div class="form-group">
                                    <label>แกลเลอรี่รูปภาพปัจจุบัน</label>
                                    <div class="row">
                                        <?php $__currentLoopData = $activity->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-3 mb-3">
                                            <div class="card">
                                                <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($image->image_path)); ?>"
                                                     class="card-img-top"
                                                     style="height: 150px; object-fit: cover;"
                                                     alt="<?php echo e($image->caption); ?>">
                                                <div class="card-body p-2">
                                                    <?php if($image->caption): ?>
                                                        <p class="card-text small mb-2"><?php echo e($image->caption); ?></p>
                                                    <?php endif; ?>
                                                    <button type="button" 
                                                            class="btn btn-danger btn-sm btn-block delete-image"
                                                            data-image-id="<?php echo e($image->id); ?>">
                                                        <i class="fas fa-trash"></i> ลบ
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                                <hr>
                                <?php endif; ?>

                                <!-- Gallery Images Section -->
                                <div class="form-group">
                                    <label>เพิ่มรูปภาพใหม่ในแกลเลอรี่</label>
                                    <div id="gallery_container">
                                        <div class="gallery-item mb-3">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="custom-file">
                                                        <input type="file"
                                                               name="gallery_images[]"
                                                               class="custom-file-input gallery-image"
                                                               accept="image/*">
                                                        <label class="custom-file-label">เลือกรูปภาพ...</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <input type="text"
                                                           name="captions[]"
                                                           class="form-control"
                                                           placeholder="คำบรรยายรูปภาพ (ไม่บังคับ)">
                                                </div>
                                            </div>
                                            <div class="image-preview mt-2" style="display: none;">
                                                <img src="" class="img-thumbnail" style="max-width: 150px;">
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" id="add_gallery_image" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-plus"></i> เพิ่มรูปภาพ
                                    </button>
                                    <small class="form-text text-muted d-block mt-2">
                                        รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB ต่อรูป)
                                    </small>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>บันทึกการแก้ไข
                                </button>
                                <a href="<?php echo e(route('admin.activities.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                                </a>
                                <a href="<?php echo e(route('activities.show', $activity)); ?>" class="btn btn-info" target="_blank">
                                    <i class="fas fa-eye me-1"></i>ดูกิจกรรม
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle cover image preview
    document.getElementById('cover_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('cover_preview');
        const previewImg = document.getElementById('cover_preview_img');
        
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
        }
    });

    // Handle gallery images
    let galleryIndex = 1;
    
    document.getElementById('add_gallery_image').addEventListener('click', function() {
        const container = document.getElementById('gallery_container');
        const newItem = document.createElement('div');
        newItem.className = 'gallery-item mb-3';
        newItem.innerHTML = `
            <div class="row">
                <div class="col-md-8">
                    <div class="custom-file">
                        <input type="file"
                               name="gallery_images[]"
                               class="custom-file-input gallery-image"
                               accept="image/*">
                        <label class="custom-file-label">เลือกรูปภาพ...</label>
                    </div>
                </div>
                <div class="col-md-3">
                    <input type="text"
                           name="captions[]"
                           class="form-control"
                           placeholder="คำบรรยายรูปภาพ (ไม่บังคับ)">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger btn-sm remove-gallery-item">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="image-preview mt-2" style="display: none;">
                <img src="" class="img-thumbnail" style="max-width: 150px;">
            </div>
        `;
        container.appendChild(newItem);
        galleryIndex++;
    });

    // Handle gallery image preview and removal
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('gallery-image')) {
            const file = e.target.files[0];
            const preview = e.target.closest('.gallery-item').querySelector('.image-preview');
            const previewImg = preview.querySelector('img');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
            }
        }
    });

    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-gallery-item') || e.target.closest('.remove-gallery-item')) {
            e.target.closest('.gallery-item').remove();
        }
    });

    // Update file input labels
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('custom-file-input')) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'เลือกรูปภาพ...';
            const label = e.target.nextElementSibling;
            label.textContent = fileName;
        }
    });

    // Handle delete existing images
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('delete-image') || e.target.closest('.delete-image')) {
            const button = e.target.classList.contains('delete-image') ? e.target : e.target.closest('.delete-image');
            const imageId = button.getAttribute('data-image-id');
            
            if (confirm('คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?')) {
                fetch(`<?php echo e(route('admin.activities.images.delete', ['activity' => $activity->id, 'image' => ''])); ?>/${imageId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        button.closest('.col-md-3').remove();
                    } else {
                        alert('เกิดข้อผิดพลาดในการลบรูปภาพ');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการลบรูปภาพ');
                });
            }
        }
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/activities/edit.blade.php ENDPATH**/ ?>