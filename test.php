<?php
echo "<PERSON><PERSON> is working!<br>";
echo "Current URL: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";

// Test if mod_rewrite is working
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "mod_rewrite is enabled<br>";
    } else {
        echo "mod_rewrite is NOT enabled<br>";
    }
} else {
    echo "Cannot check mod_rewrite status<br>";
}
?>
